package com.digiwin.escloud.aiobasic.assetrisk.controller;

import com.digiwin.escloud.aiobasic.assetrisk.model.AssetSystem;
import com.digiwin.escloud.aiobasic.assetrisk.model.dto.AssetDTO;
import com.digiwin.escloud.aiobasic.assetrisk.model.dto.AssetSystemDTO;
import com.digiwin.escloud.aiobasic.assetrisk.model.param.AssetSystemMappingParam;
import com.digiwin.escloud.aiobasic.assetrisk.model.param.AssetSystemParam;
import com.digiwin.escloud.aiobasic.assetrisk.service.AssetSystemModifyService;
import com.digiwin.escloud.common.controller.ControllerBase;
import com.digiwin.escloud.common.model.ResponseBase;
import com.digiwin.escloud.common.response.BaseResponse;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

import static com.digiwin.escloud.common.model.ResponseCode.RISK_EID_EMPTY;
import static com.digiwin.escloud.common.model.ResponseCode.SUCCESS;

@Api(value = "/risk/system", protocols = "HTTP", tags = {"资产整合相关接口"}, description = "资产整合相关接口")
@RestController
@RequestMapping("/risk/system")
public class AssetSystemModifyController extends ControllerBase {
    @Resource
    private AssetSystemModifyService assetSystemModifyService;

    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "资通系统查询,根据评估id查询该评估下的资通系统,无分页")
    @GetMapping("/systemGet")
    public BaseResponse systemGet(String assessmentId, String assetId) {
        return this.getBaseResponse((x) -> {
            List<AssetSystem> assetSystems = assetSystemModifyService.systemGet(assessmentId, assetId);
            x.setData(assetSystems);
            return x;
        }, true, false, SUCCESS);
    }

    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "资通系统查询,根据评估id查询该评估下的资通系统分页")
    @PostMapping("/systemPageGet")
    public ResponseBase systemPageGet(@RequestBody AssetSystemParam param) {
        return assetSystemModifyService.systemGet(param);
    }

    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "资通系统保存")
    @PostMapping("/systemSave")
    public ResponseBase systemSave(@RequestBody AssetSystem system) {
        return assetSystemModifyService.assetSystemSave(system);
    }

    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "资通系统更新")
    @PutMapping("/systemUpdate")
    public ResponseBase systemUpdate(@RequestBody AssetSystem system) {
        return assetSystemModifyService.assetSystemUpdate(system);
    }

    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "资通系统删除,如果删除该资通系统,会把资产与该资通得关系也全部删掉")
    @DeleteMapping("/systemDelete")
    public ResponseBase systemDelete(@RequestBody AssetSystem system) {
        return assetSystemModifyService.assetSystemDelete(system.getId());
    }


    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "分页 查询未分类的资产")
    @PostMapping("/assetNoSystem")
    public ResponseBase assetNoSystem(@RequestBody AssetSystemParam param) {
        return assetSystemModifyService.assetNoSystemGet(param);
    }

    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "分页 查询某个资通系统下的资产")
    @PostMapping("/assetSystemSome")
    public ResponseBase assetSystemSome(@RequestBody AssetSystemParam param) {
        return assetSystemModifyService.assetSystemGet(param);
    }

    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "分页 查询所有资产包括资通系统名称")
    @PostMapping("/assetSystemAll")
    public ResponseBase assetSystemAll(@RequestBody AssetSystemParam param) {
        return assetSystemModifyService.assetAllSystemGet(param);
    }


    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "资通系统和资产之间的关系管理")
    @PostMapping("/assetSystemMove")
    public ResponseBase assetSystemMove(@RequestBody AssetSystemMappingParam param) {
        if (Objects.isNull(param.getEid())) {
            return ResponseBase.error(RISK_EID_EMPTY);
        }
        assetSystemModifyService.assetSystemMove(param, param.getEid());
        return ResponseBase.ok();
    }


    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "根据资通查询资产，不分页查询所有")
    @GetMapping("/assetBySystemId")
    public ResponseBase assetBySystemId(String systemId) {
        return assetSystemModifyService.assetGet(systemId);
    }


}
