package com.digiwin.escloud.aioappupdate.release.dao;

import com.digiwin.escloud.aioappupdate.release.model.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface ReleaseMapper {
    List<BaseVersion> getBaseVersionList(Map<String, Object> map);

    BaseVersion getBaseVersion(Map<String, Object> map);

    int findBaseVersion(BaseVersion baseVersion);

    int addBaseVersion(BaseVersion baseVersion);

    int updateBaseVersion(BaseVersion baseVersion);

    int deleteBaseVersion(@Param(value = "abvId") Long abvId);

    List<Release> getReleaseList(Map<String, Object> map);

    List<Release> getReleaseTreeList(Map<String, Object> map);

    List<String> getReleaseNos(Map<String, Object> map);

    Release getNewVersion(Map<String, Object> map);

    Release getReleaseDetail(Map<String, Object> map);

    int release(Release release);

    int checkReleaseVersion(Map<String, Object> map);

    ReleaseFile getReleaseFile(@Param(value = "id") Long id);

    int insertReleaseFile(ReleaseFile releaseFile);

    int deleteReleaseFile(@Param(value = "id") Long id);

    int updateReleaseFileId(@Param(value = "arId") Long arId);

    List<ReleaseWhiteTenant> getWhiteTenants(Map<String, Object> map);

    int insertWhiteTenants(List<ReleaseWhiteTenant> whiteTenants);

    List<ReleaseWhiteTenant> getExistWhiteTenants(Map<String, Object> map);

    int deleteWhiteTenants(@Param(value = "arId") long arId,@Param(value = "arwtId") long arwtId);

    int deleteAllWhiteTenants(@Param(value = "arId") long arId);

    int updateStatus(Map<String, Object> map);

    int deleteRelease(@Param(value = "arId") Long arId);

    int setWhiteStatus(@Param(value = "arId") Long arId,@Param(value = "openWhite") Boolean openWhite);

    List<ReleaseFileDetail> getReleaseFiles(Map<String, Object> map);



}

