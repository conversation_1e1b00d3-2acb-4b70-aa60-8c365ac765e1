package com.digiwin.escloud.aioappupdate.update.model;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class UpdateProcess {
    @ApiModelProperty("id主键")
    private Long id;
    @ApiModelProperty("sid")
    private Long sid;
    @ApiModelProperty("eid")
    private Long auId;
    @ApiModelProperty("操作人")
    private String operator;
    @ApiModelProperty("操作时间")
    private String operationTime;
    @ApiModelProperty("操作")
    private String operation;
    @ApiModelProperty("操作内容")
    private String operationContent;
    @ApiModelProperty("备注")
    private String remark;

}
