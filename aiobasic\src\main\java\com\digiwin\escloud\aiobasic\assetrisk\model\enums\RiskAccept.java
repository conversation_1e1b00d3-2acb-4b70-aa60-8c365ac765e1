package com.digiwin.escloud.aiobasic.assetrisk.model.enums;

public enum RiskAccept implements CommonEnum {
    REDUCE(0, "降低"),
    TRANSFER(1, "轉移"),
    AVOID(2, "避免"),
    ACCEPT(3, "接受");
    private final int code;
    private final String desc;

    RiskAccept(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getDescription() {
        return desc;
    }

    public static RiskAccept getEnumByCode(Integer code) {
        for (RiskAccept e : RiskAccept.values()) {
            if (e.match(String.valueOf(code))) {
                return e;
            }
        }
        return null;
    }

}
