package com.digiwin.escloud.aioappupdate.update.dao;

import com.digiwin.escloud.aioappupdate.update.model.OperateLog;
import com.digiwin.escloud.aioappupdate.update.model.Update;
import com.digiwin.escloud.aioappupdate.update.model.UpdateOperation;
import com.digiwin.escloud.aioappupdate.update.model.UpdateProcess;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface UpdateMapper {
    List<Update> getUpdateLogList(Map<String, Object> map);

    List<UpdateProcess> getUpdateProgress(Map<String, Object> map);

    int stopUpdate(Map<String, Object> map);

    int saveUpdate(Update update);

    int modifyUpdate(Update update);

    Long getUpdateId(@Param(value = "updateReqNo") String updateReqNo);

    int insertUpdateProgress(UpdateProcess process);

    List<OperateLog> getOperateLogList(Map<String, Object> map);

    int saveOperateLog(OperateLog operateLog);

    String getStatus(Map<String, Object> map);

    /**
     * 依据条件字典查询更新操作列表
     * @param map 条件字典
     * @return 更新操作列表
     */
    List<UpdateOperation> selectUpdateOperationByMap(Map<String, Object> map);


}

