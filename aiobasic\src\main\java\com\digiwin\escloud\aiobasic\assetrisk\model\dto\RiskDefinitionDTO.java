package com.digiwin.escloud.aiobasic.assetrisk.model.dto;

import com.digiwin.escloud.aiobasic.assetrisk.model.RiskLevel;
import com.digiwin.escloud.aiobasic.assetrisk.model.RiskPossibilities;
import com.digiwin.escloud.aiobasic.assetrisk.model.RiskVulnerability;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
public class RiskDefinitionDTO {
    private String assessmentId;
    private List<RiskPossibilities> riskPossibilities = new ArrayList<>();
    private List<RiskVulnerability> riskVulnerabilities = new ArrayList<>();
    private List<RiskLevel> riskLevels = new ArrayList<>();

    public RiskDefinitionDTO(String assessmentId, List<RiskPossibilities> riskPossibilities,
                             List<RiskVulnerability> riskVulnerabilities, List<RiskLevel> riskLevels) {
        this.assessmentId = assessmentId;
        this.riskPossibilities = riskPossibilities;
        this.riskVulnerabilities = riskVulnerabilities;
        this.riskLevels = riskLevels;
    }
}
