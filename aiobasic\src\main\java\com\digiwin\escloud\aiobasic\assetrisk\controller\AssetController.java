package com.digiwin.escloud.aiobasic.assetrisk.controller;

import com.digiwin.escloud.aiobasic.assetrisk.model.Asset;
import com.digiwin.escloud.aiobasic.assetrisk.model.param.AssetParam;
import com.digiwin.escloud.aiobasic.assetrisk.service.AssetService;
import com.digiwin.escloud.common.controller.ControllerBase;
import com.digiwin.escloud.common.model.ResponseBase;
import com.digiwin.escloud.common.response.BaseResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import java.util.Objects;

import static com.digiwin.escloud.common.model.ResponseCode.RISK_EID_EMPTY;
import static com.digiwin.escloud.common.model.ResponseCode.SUCCESS;

@Api(value = "/risk/asset", protocols = "HTTP", tags = {"资产清单相关接口"}, description = "资产清单相关接口")
@RestController
@RequestMapping("/risk/asset")
public class AssetController extends ControllerBase {
    @Resource
    private AssetService assetService;
    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "资产清单查询，参数里的内容都可以作为查询条件，只有资产名称可以模糊匹配")
    @PostMapping("/assetGet")
    public ResponseBase assetGet(@RequestBody AssetParam param){
       return  assetService.assetSelect(param);
    }

    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "下载资产清单")
    @PostMapping("/assetDown")
    public ResponseBase assetDown(@RequestBody AssetParam param){
        if (Objects.isNull(param.getEid())) {
            return ResponseBase.error(RISK_EID_EMPTY);
        }
        return assetService.downloadAssetList(param,String.valueOf(param.getEid()));
    }

    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "单笔资产新增保存")
    @PostMapping("/assetSave")
    public ResponseBase assetSave(@RequestBody AssetParam param){
        return assetService.assetSave(param);
    }

    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "资产清单更新")
    @PutMapping("/assetUpdate")
    public ResponseBase assetUpdate(@RequestBody AssetParam param){
        return  assetService.assetUpdate(param);
    }

    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "资产作废")
    @PutMapping("/assetVoid")
    public ResponseBase assetVoid(@RequestBody AssetParam param){
        return  assetService.assetVoid(param);
    }

    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "资产从作废到启用")
    @PutMapping("/assetNormal")
    public ResponseBase assetNormal(@RequestBody AssetParam param){
        return assetService.assetNormal(param);
    }






}
