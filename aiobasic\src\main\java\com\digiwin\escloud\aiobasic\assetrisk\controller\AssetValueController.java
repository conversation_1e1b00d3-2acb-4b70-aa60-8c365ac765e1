package com.digiwin.escloud.aiobasic.assetrisk.controller;

import com.digiwin.escloud.aiobasic.assetrisk.model.AssetValueStandard;
import com.digiwin.escloud.aiobasic.assetrisk.model.dto.ProcessDTO;
import com.digiwin.escloud.aiobasic.assetrisk.model.param.AssetValueParam;
import com.digiwin.escloud.aiobasic.assetrisk.service.AssetRiskAssessmentService;
import com.digiwin.escloud.aiobasic.assetrisk.service.AssetValueService;
import com.digiwin.escloud.aiobasic.assetrisk.service.AssetValueStandardService;
import com.digiwin.escloud.common.controller.ControllerBase;
import com.digiwin.escloud.common.model.ResponseBase;
import com.digiwin.escloud.common.response.BaseResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;

import static com.digiwin.escloud.common.model.ResponseCode.RISK_EID_EMPTY;
import static com.digiwin.escloud.common.model.ResponseCode.SUCCESS;

@Api(value = "/risk/value", protocols = "HTTP", tags = {"资产鉴值相关接口"}, description = "资产鉴值相关接口")
@RestController
@RequestMapping("/risk/value")
public class AssetValueController extends ControllerBase {
    @Resource
    private AssetRiskAssessmentService assessmentService;


    @Resource
    private AssetValueService assetValueService;
    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "资产鉴值进度查询")
    @GetMapping("/assetValueProcess")
    public BaseResponse assetValueProcess(String assessmentId) {
        return this.getBaseResponse((x) -> {
            Set<String> assessmendSet = new HashSet<>();
            assessmendSet.add(assessmentId);
            Map<String, List<ProcessDTO>> map = assessmentService.assetValueProcess(assessmendSet);
            x.setData(map
            );
            return x;
        }, true, false, SUCCESS);
    }

    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "资产鉴值批量保存")
    @PostMapping("/assetValueSave")
    public ResponseBase assetValueSave(@RequestBody AssetValueParam param) {
        if (Objects.isNull(param.getEid())) {
            return ResponseBase.error(RISK_EID_EMPTY);
        }
        return assetValueService.assetValueSave(param,param.getEid());
    }





}
