package com.digiwin.escloud.aioappupdate.update.controller;

import com.digiwin.escloud.aioappupdate.update.model.OperateLog;
import com.digiwin.escloud.aioappupdate.update.model.Update;
import com.digiwin.escloud.aioappupdate.update.service.IUpdateService;
import com.digiwin.escloud.common.controller.ControllerBase;
import com.digiwin.escloud.common.response.BaseResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


@Api(value = "自动更新记录", tags = {"自动更新记录接口"})
@Slf4j
@RestController
@RequestMapping("/au/update")
public class UpdateController extends ControllerBase {
    @Autowired
    private IUpdateService updateService;

    @ApiOperation(value = "获取自动更新记录列表")
    @GetMapping(value = "/log/list")
    public BaseResponse getUpdateLogList(
            @RequestParam("pageIndex") int pageIndex,
            @RequestParam("size") int size,
            @RequestParam(value = "productCode", defaultValue = "", required = false) String productCode,
            @RequestParam(value = "baseVersion", defaultValue = "", required = false) String baseVersion,
            @RequestParam(value = "deviceName", defaultValue = "", required = false) String deviceName,
            @RequestParam(value = "tenantName", defaultValue = "", required = false) String tenantName,
            @RequestParam(value = "releaseName", defaultValue = "", required = false) String releaseName,
            @RequestParam(value = "releaseVersion", defaultValue = "", required = false) String releaseVersion,
            @RequestParam(value = "status", defaultValue = "", required = false) String status,
            @RequestParam(value = "startDate", defaultValue = "", required = false) String startDate,
            @RequestParam(value = "endDate", defaultValue = "", required = false) String endDate,
            @RequestParam(value = "updateChannel", defaultValue = "", required = false) String updateChannel) {
        return this.getBaseResponse(() -> updateService.getUpdateLogList(pageIndex, size, productCode, baseVersion,
                deviceName, tenantName, releaseName, releaseVersion, status, startDate, endDate, updateChannel),
                false, false, null);
    }

    @ApiOperation(value = "发起请求：存自动/手动更新记录")
    @PostMapping(value = "/log/sendRequest")
    public BaseResponse sendRequest(
            @ApiParam(value = "产品代号", required = true)
            @RequestParam(value = "productCode") String productCode,
            @ApiParam(value = "基本版本", required = true)
            @RequestParam(value = "baseVersion") String baseVersion,
            @ApiParam(value = "依赖的平台版号")
            @RequestParam(value = "platformVersion", defaultValue = "", required = false) String platformVersion,
            @ApiParam(value = "版更包版号", required = true)
            @RequestParam(value = "releaseVersion") String releaseVersion,
            @ApiParam(value = "版更包编号")
            @RequestParam(value = "releaseNo", required = false) String releaseNo,
            @ApiParam(value = "客服代号")
            @RequestParam(value = "serviceCode", required = false) String serviceCode,
            @ApiParam("收集项代号，为空时失败也不产生预警")
            @RequestParam(value = "collectConfigCode", defaultValue = "", required = false) String collectCode,
            @ApiParam("预警项目代号，为空时失败也不产生预警")
            @RequestParam(value = "warningItemCode", defaultValue = "", required = false) String warningCode,
            @ApiParam("更新日志内容")
            @RequestBody Update update) {
        return this.getBaseResponse(() -> updateService.sendRequest(productCode, baseVersion, platformVersion,
                releaseVersion, releaseNo, serviceCode, collectCode, warningCode, update),
                false, false, null);
    }

    @ApiOperation(value = "有请求单号：存自动/手动更新记录")
    @PostMapping(value = "/log/save")
    public BaseResponse saveUpdateLog(
            @ApiParam(value = "产品代号")
            @RequestParam(value = "productCode", defaultValue = "", required = false) String productCode,
            @ApiParam(value = "基本版本")
            @RequestParam(value = "baseVersion", defaultValue = "", required = false) String baseVersion,
            @ApiParam(value = "依赖的平台版号")
            @RequestParam(value = "platformVersion", defaultValue = "", required = false) String platformVersion,
            @ApiParam(value = "版更包版号")
            @RequestParam(value = "releaseVersion", defaultValue = "", required = false) String releaseVersion,
            @ApiParam(value = "客服代号")
            @RequestParam(value = "serviceCode", required = false) String serviceCode,
            @ApiParam(value = "更新序号")
            @RequestParam(value = "updateReqNo") String updateReqNo,
            @ApiParam("收集项代号，为空时失败也不产生预警")
            @RequestParam(value = "collectConfigCode", defaultValue = "", required = false) String collectConfigCode,
            @ApiParam("预警项目代号，为空时失败也不产生预警")
            @RequestParam(value = "warningItemCode", defaultValue = "", required = false) String warningItemCode,
            @ApiParam("更新日志内容")
            @RequestBody Update update) {
        return this.getBaseResponse(() -> updateService.saveUpdateLog(productCode, baseVersion, platformVersion,
                releaseVersion, serviceCode, updateReqNo, collectConfigCode, warningItemCode, update),
                false, false, null);
    }

    @ApiOperation(value = "根据请求单号 返回单头状态")
    @GetMapping(value = "/log/status")
    public BaseResponse getStatus(
            @RequestParam(value = "updateReqNo") String updateReqNo) {
        return this.getBaseResponse(() -> updateService.getStatus(updateReqNo),
                false, false, null);
    }

    @ApiOperation(value = "停止更新")
    @PutMapping(value = "/log/stop")
    public BaseResponse stopUpdate(
            @RequestParam(value = "id", defaultValue = "", required = false) Long id,
            @RequestParam(value = "operationContent", defaultValue = "", required = false) String operationContent,
            @RequestParam(value = "status", defaultValue = "HAND_STOP", required = false) String status) {
        return this.getBaseResponse(() -> updateService.stopUpdate(id, status, operationContent),
                false, false, null);
    }

    @ApiOperation(value = "获取更新记录过程")
    @GetMapping(value = "/log/progress")
    public BaseResponse getUpdateProgress(
            @RequestParam(value = "auId", defaultValue = "", required = false) Long auId) {
        return this.getBaseResponse(() -> updateService.getUpdateProgress(auId),
                false, false, null);
    }

    @ApiOperation(value = "获取客户操作日志")
    @GetMapping(value = "/operate/log/list")
    public BaseResponse getOperateLogList(
            @RequestParam("pageIndex") int pageIndex,
            @RequestParam("size") int size,
            @RequestParam(value = "tenantName", defaultValue = "", required = false) String tenantName,
            @RequestParam(value = "deviceName", defaultValue = "", required = false) String deviceName,
            @RequestParam(value = "productCode", defaultValue = "", required = false) String productCode,
            @RequestParam(value = "operation", defaultValue = "", required = false) String operation,
            @RequestParam(value = "status", defaultValue = "", required = false) String status,
            @RequestParam(value = "releaseVersion", defaultValue = "", required = false) String releaseVersion,
            @RequestParam(value = "releaseName", defaultValue = "", required = false) String releaseName,
            @RequestParam(value = "startDate", defaultValue = "", required = false) String startDate,
            @RequestParam(value = "endDate", defaultValue = "", required = false) String endDate) {
        return this.getBaseResponse(() -> updateService.getOperateLogList(pageIndex, size, tenantName, deviceName, productCode, operation, status, releaseVersion, releaseName, startDate, endDate),
                false, false, null);
    }

    @ApiOperation(value = "保存客户操作记录")
    @PostMapping(value = "/operate/log")
    public BaseResponse saveOperateLog(
            @RequestParam(value = "serviceCode", required = false) String serviceCode,
            @RequestBody OperateLog operateLog) {
        return this.getBaseResponse(() -> updateService.saveOperateLog(serviceCode, operateLog),
                false, false, null);
    }

    @ApiOperation(value = "获取更新操作")
    @GetMapping(value = "/operation/list")
    public BaseResponse getUpdateOperationList(
            @RequestParam(value = "resultType", required = false) String resultType) {
        return this.getBaseResponse(() -> updateService.getUpdateOperationList(resultType),
                false, false, null);
    }
}
