package com.digiwin.escloud.aioappupdate.release.model;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class Release {
    @ApiModelProperty("id主键")
    private Long id;
    @ApiModelProperty("sid")
    private Long sid;
    @ApiModelProperty("产品/应用编码")
    private String productCode;
    @ApiModelProperty("产品/应用名称")
    private String productCategory;
    @ApiModelProperty("应用基版表主键id")
    private Long abvId;
    @ApiModelProperty("依赖的平台版本")
    private String platformVersion;
    @ApiModelProperty("版更包的版本号")
    private String releaseVersion;
    @ApiModelProperty("用于排序的版本号")
    private String sortVersion;
    @ApiModelProperty("版更包编号")
    private String releaseNo;
    @ApiModelProperty("版更包的版本名称")
    private String releaseName;
    @ApiModelProperty("功能描述")
    private String description;
    @ApiModelProperty("版更说明")
    private String releaseExplain;
    @ApiModelProperty("文件表主键id")
    private Long arfId;
    @ApiModelProperty("文件类型 地址|文件")
    private String fileType;
    @ApiModelProperty("自动更新脚本")
    private String autoUpdateScript;
    @ApiModelProperty("发布人")
    private String releaser;
    @ApiModelProperty("发布时间")
    private String releaseTime;
    @ApiModelProperty("版更状态")
    private int status;
    @ApiModelProperty("是否开启白名单 0 ：不开启  | 1： 开启")
    private boolean isOpenWhite;
    @ApiModelProperty("版更文件")
    private ReleaseFile releaseFile;
    @ApiModelProperty("白名单租户列表")
    private List<ReleaseWhiteTenant> whiteTenants;
    @ApiModelProperty("基版")
    private BaseVersion baseVersion;
}
