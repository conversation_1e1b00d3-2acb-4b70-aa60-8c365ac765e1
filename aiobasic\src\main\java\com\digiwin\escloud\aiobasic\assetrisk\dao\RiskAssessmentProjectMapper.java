package com.digiwin.escloud.aiobasic.assetrisk.dao;

import com.digiwin.escloud.aiobasic.assetrisk.model.AssetSystemAssessmentTypeMapping;
import com.digiwin.escloud.aiobasic.assetrisk.model.RiskAssessmentProject;
import com.digiwin.escloud.aiobasic.assetrisk.model.RiskAssessmentType;
import com.digiwin.escloud.aiobasic.assetrisk.model.param.RiskAssessmentProjectParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

public interface RiskAssessmentProjectMapper {
    int insertProject(RiskAssessmentProject project);
    int insertType(RiskAssessmentType project);
    int updateProject(RiskAssessmentProject project);
    int updateType(RiskAssessmentType project);
    int deleteProject(String id);
    int deleteTypeById(String id);
    List<RiskAssessmentProject> selectProjectByConditions(RiskAssessmentProjectParam param);
    List<RiskAssessmentProject> selectProjectByTypeId(String typeId);
    List<RiskAssessmentProject> selectProjectByTypes(@Param("types") Set<RiskAssessmentType> types,@Param("eid") Long eid);
    List<RiskAssessmentProject> selectProjectBySystemIds(@Param("assetSystemIds") Set<String> assetSystemIds,@Param("eid") Long eid);
    String selectPlanIdUsed(@Param("improveControlPlanId") String improveControlPlanId,@Param("assessmentId") String assessmentId);
    List<RiskAssessmentType> selectAllType(String typeName);
    RiskAssessmentType selectTypeById(String id);
    RiskAssessmentType selectTypeByProjectId(@Param("id") String projectId);
    String selectProjectTypeIdIsUsed(@Param("id") String id);
    List<AssetSystemAssessmentTypeMapping> selectSystemProjectTypeMapping(@Param("assetSystemId") String systemId);
    void deleteSelectSystemProjectTypeMapping(@Param("assetSystemId") String systemId
            ,@Param("assessmentTypeIds") Set<String> assessmentTypeIds);
    List<String> selectTypeByTypeName(@Param("typeName") String typeName,@Param("id") String id);
    void insertSystemProjectTypeMapping(AssetSystemAssessmentTypeMapping mapping);
}
