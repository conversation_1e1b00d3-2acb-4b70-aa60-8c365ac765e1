package com.digiwin.escloud.aiobasic.assetrisk.model.dto;

import com.digiwin.escloud.aiobasic.assetrisk.model.RiskImprovePlan;
import com.digiwin.escloud.aiobasic.assetrisk.model.base.AssetRiseAssessmentBase;
import com.digiwin.escloud.aiobasic.assetrisk.model.enums.DoPriority;
import com.digiwin.escloud.aiobasic.assetrisk.model.enums.PlanStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.*;

@EqualsAndHashCode(callSuper = true)
@Data
public class RiskImprovePlan2DTO extends AssetRiseAssessmentBase {
    private int improveNo;
    private DoPriority doPriority;
    private String controlPlanId;
    private String assessmentId;
    private String improvePlan;
    private String object;
    private Set<RiskImprovePlanSystemDTO> effectSystem = new HashSet<>();
    private String benefit;
    private PlanStatus planStatus;
    private String responsibleDepartment;
    private String head;
    private String headMail;
    //预计执行时间
    private Date estimateExecutionTime;
    //实际开始时间
    private Date actualStartTime;
    //预计完成时间
    private Date estimateCompleteTime;
    //预计完成时间
    private Date  actualCompleteTime;



}
