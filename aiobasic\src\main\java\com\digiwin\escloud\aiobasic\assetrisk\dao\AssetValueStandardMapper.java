package com.digiwin.escloud.aiobasic.assetrisk.dao;

import com.digiwin.escloud.aiobasic.assetrisk.model.Asset;
import com.digiwin.escloud.aiobasic.assetrisk.model.AssetValueStandard;
import com.digiwin.escloud.aiobasic.assetrisk.model.dto.ProcessDTO;
import com.digiwin.escloud.aiobasic.assetrisk.model.param.AssetParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface AssetValueStandardMapper {

    int insertRiskAssetValueStandard(AssetValueStandard standard);
    int deleteRiskAssetValueStandardById(String id);
    int updateRiskAssetValueStandard(AssetValueStandard standard);
    List<AssetValueStandard> selectRiskAssetValueStandardByAssessmentId(@Param("assessmentId") String assessmentId,@Param("score") Integer level);
    String selectStandardRiskSystemIdByAssessmentId(@Param("assessmentId") String assessmentId);
}
