package com.digiwin.escloud.aioappupdate.release.model;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ReleaseFile {
    @ApiModelProperty("id主键")
    private Long id;
    @ApiModelProperty("sid")
    private Long sid;
    @ApiModelProperty("文件id")
    private String fileId;
    @ApiModelProperty("文件名")
    private String fileName;
    @ApiModelProperty("文件大小")
    private String fileSize;
    @ApiModelProperty("文件地址")
    private String url;
    @ApiModelProperty("文件上传时间")
    private String uploadTime;
}
