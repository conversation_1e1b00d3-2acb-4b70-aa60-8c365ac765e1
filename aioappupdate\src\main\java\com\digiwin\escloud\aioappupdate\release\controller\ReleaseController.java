package com.digiwin.escloud.aioappupdate.release.controller;

import com.digiwin.escloud.aioappupdate.release.model.BaseVersion;
import com.digiwin.escloud.aioappupdate.release.model.Release;
import com.digiwin.escloud.aioappupdate.release.model.ReleaseWhiteTenant;
import com.digiwin.escloud.aioappupdate.release.service.IReleaseService;
import com.digiwin.escloud.common.controller.ControllerBase;
import com.digiwin.escloud.common.response.BaseResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@Api(value = "自动更新发布", tags = {"自动更新发布接口"})
@Slf4j
@RestController
@RequestMapping("/au")
public class ReleaseController extends ControllerBase {
    @Autowired
    private IReleaseService releaseService;

    @ApiOperation(value = "获取基版列表")
    @GetMapping(value = "/base/version/list")
    public BaseResponse getBaseVersionList(@RequestParam("pageIndex") int pageIndex,
                                           @RequestParam("size") int size,
                                           @RequestParam(value = "productCode", defaultValue = "", required = false) String productCode) {
        return this.getBaseResponse(()-> releaseService.getBaseVersionList(pageIndex, size, productCode),
                false, false, null);
    }


    @ApiOperation(value = "新增基版")
    @PostMapping(value = "/base/version")
    public BaseResponse addBaseVersion(@RequestBody BaseVersion baseVersion) {
        return this.getBaseResponse(()-> releaseService.addBaseVersion(baseVersion),
                false, false, null);
    }

    @ApiOperation(value = "更新基版")
    @PutMapping(value = "/base/version/{abvId}")
    public BaseResponse updateBaseVersion(@PathVariable(name = "abvId") Long abvId,
                                          @RequestBody BaseVersion baseVersion) {
        return this.getBaseResponse(()-> releaseService.updateBaseVersion(abvId,baseVersion),
                false, false, null);
    }

    @ApiOperation(value = "删除基版")
    @DeleteMapping(value = "/base/version/{abvId}")
    public BaseResponse deleteBaseVersion(@PathVariable(name = "abvId") Long abvId) {
        return this.getBaseResponse(()-> releaseService.deleteBaseVersion(abvId),
                false, false, null);
    }

    @ApiOperation(value = "获取版更包列表")
    @GetMapping(value = "/release/list")
    public BaseResponse getReleaseList(@RequestParam("pageIndex") int pageIndex,
                                       @RequestParam("size") int size,
                                       @RequestParam(value = "productCode", defaultValue = "", required = false) String productCode,
                                       @RequestParam(value = "baseVersion", defaultValue = "", required = false) String baseVersion,
                                       @RequestParam(value = "abvId", defaultValue = "", required = false) Long abvId,
                                       @RequestParam(value = "status", defaultValue = "", required = false) String status,
                                       @RequestParam(value = "releaseVersion", defaultValue = "", required = false) String releaseVersion,
                                       @RequestParam(value = "releaseName", defaultValue = "", required = false) String releaseName,
                                       @RequestParam(value = "platformVersion", defaultValue = "", required = false) String platformVersion,
                                       @RequestParam(value = "startDate", defaultValue = "", required = false) String startDate,
                                       @RequestParam(value = "endDate", defaultValue = "", required = false) String endDate,
                                       @RequestParam(value = "openWhite", defaultValue = "", required = false) String openWhite) {
        return this.getBaseResponse(()-> releaseService.getReleaseList(pageIndex, size, productCode, baseVersion, abvId, status, releaseVersion, releaseName, platformVersion, startDate, endDate, openWhite),
                false, false, null);
    }

    @ApiOperation(value = "获取版更包列表（不分页）")
    @GetMapping(value = "/release/tree/list")
    public BaseResponse getReleaseTreeList(@RequestParam(value = "productCode", defaultValue = "", required = false) String productCode,
                                       @RequestParam(value = "baseVersion", defaultValue = "", required = false) String baseVersion,
                                       @RequestParam(value = "abvId", defaultValue = "", required = false) Long abvId,
                                       @RequestParam(value = "status", defaultValue = "", required = false) String status,
                                       @RequestParam(value = "releaseVersion", defaultValue = "", required = false) String releaseVersion,
                                       @RequestParam(value = "releaseName", defaultValue = "", required = false) String releaseName,
                                       @RequestParam(value = "platformVersion", defaultValue = "", required = false) String platformVersion,
                                       @RequestParam(value = "startDate", defaultValue = "", required = false) String startDate,
                                       @RequestParam(value = "endDate", defaultValue = "", required = false) String endDate,
                                       @RequestParam(value = "openWhite", defaultValue = "", required = false) String openWhite) {
        return this.getBaseResponse(()-> releaseService.getReleaseTreeList(productCode, baseVersion, abvId, status, releaseVersion, releaseName, platformVersion, startDate, endDate, openWhite),
                false, false, null);
    }

    @ApiOperation(value = "获取版更详情")
    @GetMapping(value = "/release/detail")
    public BaseResponse getReleaseDetail(@RequestParam Long arId) {
        return this.getBaseResponse(()-> releaseService.getReleaseDetail(arId),
                false, false, null);
    }

    @ApiOperation(value = "编辑，保存版更包")
    @PostMapping(value = "/release")
    public BaseResponse release(@RequestParam(value = "bucketName", defaultValue = "au", required = false) String bucketName,
                                @RequestParam(value = "fileId", defaultValue = "", required = false) String fileId,
                                @RequestBody Release release) {
        return this.getBaseResponse(()-> releaseService.release(bucketName, fileId, release),
                false, false, null);
    }

    @ApiOperation(value = "发布、取消发布")
    @PutMapping(value = "/release/status")
    public BaseResponse updateStatus(@RequestParam(value = "arId") Long arId,@RequestParam(value = "status") int status) {
        return this.getBaseResponse(()-> releaseService.updateStatus(arId, status),
                false, false, null);
    }

    @ApiOperation(value = "设置白名单开关")
    @PutMapping(value = "/release/{arId}/white/status")
    public BaseResponse setWhiteStatus(@PathVariable(name = "arId") Long arId,
                                       @RequestParam(value = "openWhite") boolean openWhite) {
        return this.getBaseResponse(()-> releaseService.setWhiteStatus(arId, openWhite),
                false, false, null);
    }

    @ApiOperation(value = "设置白名单租户(添加)")
    @PostMapping(value = "/release/{arId}/white/tenant")
    public BaseResponse setWhiteTanant(@PathVariable(name = "arId") Long arId, @RequestBody Release release) {
        return this.getBaseResponse(()-> releaseService.setWhiteTanant(arId, release),
                false, false, null);
    }

    @ApiOperation(value = "批量设置白名单租户(添加)")
    @PostMapping(value = "/release/{arId}/white/tenant/batch")
    public BaseResponse batchSetWhiteTanant(@PathVariable(name = "arId") Long arId, @RequestBody List<ReleaseWhiteTenant> whiteTenants) {
        return this.getBaseResponse(()-> releaseService.batchSetWhiteTanant(arId, whiteTenants),
                false, false, null);
    }

    @ApiOperation(value = "设置白名单租户(删除)")
    @DeleteMapping(value = "/release/{arId}/white/tenant")
    public BaseResponse deleteWhiteTanant(@PathVariable(name = "arId") long arId, @RequestParam long arwtId) {
        return this.getBaseResponse(()-> releaseService.deleteWhiteTanant(arId, arwtId),
                false, false, null);
    }

    @ApiOperation(value = "查看白名单租户")
    @GetMapping(value = "/release/{arId}/white/tenant")
    public BaseResponse getWhiteTanant(@PathVariable(name = "arId") Long arId) {
        return this.getBaseResponse(()-> releaseService.getWhiteTanant(arId),
                false, false, null);
    }

    @ApiOperation(value = "删除版更记录")
    @DeleteMapping(value = "/release/{arId}")
    public BaseResponse deleteRelease(@PathVariable(name = "arId") Long arId) {
        return this.getBaseResponse(()-> releaseService.deleteRelease(arId),
                false, false, null);
    }



    @ApiOperation(value = "查看版更包文件")
    @GetMapping(value = "/release/file")
    public BaseResponse getReleaseFiles(@RequestParam(value = "productCode", defaultValue = "", required = false) String productCode,
                                        @RequestParam(value = "baseVersion", defaultValue = "", required = false) String baseVersion,
                                        @RequestParam(value = "status", defaultValue = "1", required = false) int status,
                                        @RequestParam("pageIndex") int pageIndex,
                                        @RequestParam("size") int size) {
        return this.getBaseResponse(()-> releaseService.getReleaseFiles(productCode, baseVersion, status, pageIndex, size),
                false, false, null);
    }

    @ApiOperation(value = "删除版更包文件")
    @DeleteMapping(value = "/release/file/{id}")
    public BaseResponse deleteReleaseFile(@PathVariable(name = "id") Long id,
                                          @RequestParam(value = "arId") Long arId,
                                          @RequestParam(value = "fileId") String fileId,
                                          @RequestParam(value = "bucketName", defaultValue = "au", required = false) String bucketName) {
        return this.getBaseResponse(()-> releaseService.deleteReleaseFile(id, arId, fileId, bucketName),
                false, false, null);
    }

    @ApiOperation(value = "查询版更包最新版本")
    @GetMapping(value = "/release/new/version")
    public BaseResponse getNewVersion(@RequestParam(value = "productCode") String productCode,
                                      @RequestParam(value = "baseVersion") String baseVersion,
                                      @RequestParam(value = "platformVersion",required = false) String platformVersion,
                                      @RequestParam(value = "releaseNo",required = false) String releaseNo,
                                      @RequestParam(value = "currentVersion",required = false) String currentVersion,
                                      @RequestParam(value = "serviceCode",required = false) String serviceCode) {
        return this.getBaseResponse(()-> releaseService.getNewVersion(productCode, baseVersion, platformVersion, releaseNo, currentVersion, serviceCode),
                false, false, null);
    }

}
