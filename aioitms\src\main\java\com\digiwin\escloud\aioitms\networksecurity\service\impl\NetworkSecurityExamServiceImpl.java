package com.digiwin.escloud.aioitms.networksecurity.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.digiwin.escloud.aiochat.model.NetworkSecurityReportRequest;
import com.digiwin.escloud.aiocmdb.model.*;
import com.digiwin.escloud.aioitms.bigdata.ApiController;
import com.digiwin.escloud.aioitms.bigdata.BigDataUtil;
import com.digiwin.escloud.aioitms.bigdata.NetworkSecurityDataParams;
import com.digiwin.escloud.aioitms.bigdata.SaveParams;
import com.digiwin.escloud.aioitms.bigdata.model.Query;
import com.digiwin.escloud.aioitms.bigdata.service.WarningService;
import com.digiwin.escloud.aioitms.bigdata.service.impl.ApiService;
import com.digiwin.escloud.aioitms.exam.dao.AiopsExamMapper;
import com.digiwin.escloud.aioitms.exam.dao.AiopsExamRecordMapper;
import com.digiwin.escloud.aioitms.exam.dao.AiopsExamIndexReportAdditionalContentMapper;
import com.digiwin.escloud.aioitms.exam.model.*;
import com.digiwin.escloud.aioitms.exam.service.AiopsExamService;
import com.digiwin.escloud.aioitms.instance.dao.InstanceMapper;
import com.digiwin.escloud.aioitms.instance.model.AiopsInstance;
import com.digiwin.escloud.aioitms.instance.model.AiopsItem;
import com.digiwin.escloud.aioitms.instance.model.AiopsItemExtend;
import com.digiwin.escloud.aioitms.instance.model.MergeStatisticsDetailInfo;
import com.digiwin.escloud.aioitms.model.authorize.AiopsAuthStatus;
import com.digiwin.escloud.aioitms.networksecurity.dao.NetworkSecurityExamMapper;
import com.digiwin.escloud.aioitms.networksecurity.model.NetworkSecurityExaminationProjectType;
import com.digiwin.escloud.aioitms.networksecurity.model.OrganizationalModel;
import com.digiwin.escloud.aioitms.networksecurity.model.request.NetworkSecurityExamAssetRequest;
import com.digiwin.escloud.aioitms.networksecurity.model.request.NetworkSecurityExamRequest;
import com.digiwin.escloud.aioitms.networksecurity.service.NetworkSecurityExamService;
import com.digiwin.escloud.aioitms.networksecurity.service.NetworkSecurityExamSimpleEsReport;
import com.digiwin.escloud.aioitms.report.model.ReportStatus;
import com.digiwin.escloud.aioitms.report.service.serviceReprot.NetworkSecurityExamEsReport;
import com.digiwin.escloud.aioitms.util.RestUtil;
import com.digiwin.escloud.common.controller.ParamCheckHelp;
import com.digiwin.escloud.common.feign.AioChatFeignClient;
import com.digiwin.escloud.common.model.ResponseBase;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.common.util.*;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.digiwin.escloud.aioitms.common.Constants.BigData.*;
import static com.digiwin.escloud.aioitms.exam.model.enums.AiopsExamStatus.NOT_START;
import static com.digiwin.escloud.common.model.ResponseCode.*;

@Service
@Slf4j
public class NetworkSecurityExamServiceImpl implements NetworkSecurityExamService, ParamCheckHelp {

    private static final String NETWORK_SECURITY_EXAM_PORJECT_TOP_LEVEL = "TOP";
    private static final String NETWORK_SECURITY_EXAM_PORJECT_LOCALIZATION = "Localization";
    private static final String NETWORK_SECURITY_EXAM_PORJECT_ORGANIZATIONAL = "Organizational";
    private static final String NETWORK_SECURITY_EXAM_PORJECT_ASSET = "Asset";
    private static final String NETWORK_SECURITY_COMMON_DEVICE_NAME = "deviceName";

    private static final String MODEL_DB = "servicecloud.";

    private static final List<AiopsAuthStatus> INSTANCE_STATUS_LIST = Lists.newArrayList(
            AiopsAuthStatus.AUTHED,
            AiopsAuthStatus.UNAUTH,
            AiopsAuthStatus.NONE);
    @Resource
    private NetworkSecurityExamMapper networkSecurityExamMapper;

    @Resource
    private AiopsExamRecordMapper aiopsExamRecordMapper;

    @Resource
    private AiopsExamMapper aiopsExamMapper;

    @Resource
    private BigDataUtil bigDataUtil;

    @Resource
    AiopsExamService aiopsExamService;

    @Resource
    WarningService warningService;

    @Resource
    private NetworkSecurityExamEsReport networkSecurityExamEsReport;

    @Resource
    private InstanceMapper instanceMapper;

    @Resource
    private RestUtil restUtil;

    @Resource
    ApiService apiService;

    @Resource
    private ApiController apiController;

    @Resource
    private AioChatFeignClient aioChatFeignClient;

    @Resource
    private NetworkSecurityExamSimpleEsReport networkSecurityExamSimpleEsReport;

    @Resource
    private AiopsExamIndexReportAdditionalContentMapper aiopsExamIndexReportAdditionalContentMapper;

    @Override
    public ResponseBase getNetworkSecurityExam(NetworkSecurityExamRequest request) {
        // 1. 查询aiops_exam表获取id
        ResponseBase<List<Long>> ae = getNetworkSecurityAeBatch(Lists.newArrayList("Network_Security_Examination","Network_Security_Examination_Simple"));
        if (!ae.checkIsSuccess()) {
            return ae;
        }

        request.setAeIdList(ae.getData());
        // 2. 分页查询aiops_exam_record
        PageHelper.startPage(request.getPageNum(), request.getPageSize());
        List<AiopsExamRecord> records = networkSecurityExamMapper.selectExamRecordsByExamId(request);
        PageInfo<AiopsExamRecord> aiopsExamRecordPageInfo = new PageInfo<>(records);
        if (CollectionUtils.isEmpty(records)) {
            return ResponseBase.ok(aiopsExamRecordPageInfo);
        }

        // 获取所有记录ID
        List<Long> aerIds = records.stream()
                .map(AiopsExamRecord::getId)
                .collect(Collectors.toList());

        // 批量查询分数记录
        List<AiopsExamItemInstanceScore> scores = aiopsExamRecordMapper
                .selectScoresByAerIdListOrNetworkExamCategoryCode(aerIds, null);

        // 如果没有分数记录，直接返回结果
        if (CollectionUtils.isEmpty(scores)) {
            return ResponseBase.ok(aiopsExamRecordPageInfo);
        }

        // 提取唯一的模型代码和项目ID
        List<String> modelCodeList = scores.stream()
                .map(AiopsExamItemInstanceScore::getModelCode)
                .distinct()
                .collect(Collectors.toList());

        List<String> aiopsItemIdList = scores.stream()
                .map(AiopsExamItemInstanceScore::getAiopsItemId)
                .distinct()
                .collect(Collectors.toList());

        List<String> assetIdList = scores.stream()
                .map(AiopsExamItemInstanceScore::getNetworkExamAssetId)
                .map(LongUtil::safeToString)
                .distinct()
                .collect(Collectors.toList());

        ResponseBase<List<NetworkSecurityExaminationProjectType>> projectTypeRb = getProjectType(null, null, NETWORK_SECURITY_EXAM_PORJECT_ASSET, true, null, null);

        if (projectTypeRb.checkIsSuccess()) {
            List<NetworkSecurityExaminationProjectType> projectTypeList = projectTypeRb.getData();
            String sql = "";
            for (int i = 0, projectTypeListSize = projectTypeList.size(); i < projectTypeListSize; i++) {
                NetworkSecurityExaminationProjectType projectType = projectTypeList.get(i);
                String modelPk = projectType.getModelPk();
                // 构建SQL查询设备名称
                String where = String.format(AIOPS_ITEMID + "  in ('%s')",
                        StringUtils.join(aiopsItemIdList, "','"));

                // 构建SQL查询设备名称
                String idWhere = String.format(modelPk + "  in ('%s')",
                        StringUtils.join(assetIdList, "','"));

                sql += "SELECT "+modelPk+" as id,aiopsItemId, "+NETWORK_SECURITY_COMMON_DEVICE_NAME+"  FROM " + MODEL_DB + projectType.getModelCode() + " WHERE " + where + " AND " + idWhere;

                if (i != projectTypeListSize - 1) {
                    sql += " UNION ALL ";
                }
            }

            List<Map<String, Object>> aiopsItemIdDeviceNameList = bigDataUtil.srQuery(sql);

            // 创建aiopsItemId到deviceName的映射
            Map<String, String> aiopsItemIdDeviceNameMap = aiopsItemIdDeviceNameList.stream()
                    .collect(Collectors.toMap(
                            k -> StringUtil.toString(k.get(AIOPS_ITEMID))+"_"+StringUtil.toString(k.get("id")),
                            v -> StringUtil.toString(v.getOrDefault(NETWORK_SECURITY_COMMON_DEVICE_NAME,"")),
                            (o, n) -> n));

            // 按aerId分组scores
            Map<Long, List<AiopsExamItemInstanceScore>> scoreMap = scores.stream()
                    .peek(score -> score.setDeviceName(aiopsItemIdDeviceNameMap.get(score.getAiopsItemId()+"_"+score.getNetworkExamAssetId())))
                    .collect(Collectors.groupingBy(AiopsExamItemInstanceScore::getAerId));

            records.forEach(record -> {
                record.setAeiisList(scoreMap.getOrDefault(record.getId(), Collections.emptyList()));
            });
        }



        // 查询报告记录
        List<AiopsExamRecordsReportRecord> aerrrList = networkSecurityExamMapper.selectReportByAerIds(aerIds);

        // 查询体检指标健康状况
        List<AiopsExamLevelSetting> aelsList = aiopsExamMapper.selectAiopsExamLevel();



        // 按aerId分组reports
        Map<Long, List<AiopsExamRecordsReportRecord>> reportMap = aerrrList.stream()
                .collect(Collectors.groupingBy(AiopsExamRecordsReportRecord::getAerId));

        // 将查询结果注入到每个记录中
        records.forEach(record -> {
            record.setAelsList(aelsList);
            record.setAerrrList(reportMap.getOrDefault(record.getId(), Collections.emptyList()));
        });

        return ResponseBase.ok(aiopsExamRecordPageInfo);
    }


    @Override
    public BaseResponse saveNetworkSecurityExamRecord(AiopsExamRecord record) {

        Optional<BaseResponse> optResponse = checkParamIsEmpty(record.getExamEnv(), "examEnv");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        optResponse = checkParamIsEmpty(record.getExamTitle(), "examTitle");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        ResponseBase<Long> ae = getNetworkSecurityAe(record);
        if (!ae.checkIsSuccess()) {
            return BaseResponse.error(ae);
        }

        record.setAeId(ae.getData());
        record.setId(SnowFlake.getInstance().newId());
        record.setSid(RequestUtil.getHeaderSid());
        record.setExamStartTime(LocalDateTime.now());
        record.setExamStatus(NOT_START);
        try {
            aiopsExamRecordMapper.insertAiopsExamRecord(record);

        } catch (DuplicateKeyException de) {
            return BaseResponse.error(AIOPS_EXAM_RECORD_TITLE_DUPLICATE_ERROR);
        } catch (Exception e) {
            return BaseResponse.error(AIOPS_EXAM_RECORD_SAVE_ERROR);
        }
        return BaseResponse.ok(record.getId());
    }

    @Override
    public ResponseBase saveOrUpdateProjectType(NetworkSecurityExaminationProjectType projectType) {

        if (projectType.getId() == null) {
            projectType.setId(SnowFlake.getInstance().newId());
            projectType.setCreateTime(LocalDateTime.now());
        }
        projectType.setUpdateTime(LocalDateTime.now());
        int count = networkSecurityExamMapper.insertOrUpdateProjectType(projectType);
        return ResponseBase.ok(count);
    }

    @Override
    public ResponseBase<List<NetworkSecurityExaminationProjectType>> getProjectType(String categoryCode, String categoryName,
                                                                                    String parentCode, Boolean filterNullModel, Long aerId, Long eid) {
        // 1. 获取项目类型列表
        List<NetworkSecurityExaminationProjectType> projectTypes = getProjectTypeList(categoryCode, categoryName, parentCode, filterNullModel);
        if (CollectionUtils.isEmpty(projectTypes)) {
            return ResponseBase.ok(Collections.emptyList());
        }

        // 2. 获取模型代码列表
        List<String> modelCodeList = getModelCodeListFromProjectTypes(projectTypes);

        // 3. 获取数据计数
        Map<String, Object> modelCountMap = LongUtil.isNotEmpty(aerId)
                ? querySelectedCount(projectTypes, aerId)
                : queryModelCount(modelCodeList,eid).stream()
                .collect(Collectors.toMap(
                        map -> map.get("modelCode").toString(),
                        map -> Integer.parseInt(map.get("cnt").toString()),
                        (oldValue, newValue) -> oldValue,
                        LinkedHashMap::new
                ));
        Map<String, String> modelPKMap = getModelPK(modelCodeList);
        // 4. 处理每个项目类型的详细信息
        for (NetworkSecurityExaminationProjectType projectType : projectTypes) {
            // 设置数据计数
            projectType.setDataCount(IntegerUtil.objectToInteger(modelCountMap.getOrDefault(projectType.getModelCode(), 0)));

            if (StringUtils.isEmpty(projectType.getModelCode())) {
                continue;
            }

            // 获取ETL详情
            processEtlDetails(projectType, eid, modelPKMap);
        }

        return ResponseBase.okT(projectTypes);
    }


    private void processEtlDetails(NetworkSecurityExaminationProjectType projectType, Long eid, Map<String, String> modelPKMap) {
        try {
            // 设置主键
            String[] pkArray = Objects.requireNonNull(modelPKMap.get(projectType.getModelCode())).split(",");
            projectType.setModelPk(pkArray[0]);

            // 处理特殊类型的项目
            if (isSpecialProjectType(projectType.getCategoryCode())) {
                processSpecialProjectType(projectType, eid);
            }
        } catch (Exception e) {
            log.error("处理ETL详情失败: {}", e.getMessage(), e);
        }
    }

    private Map<String,String> getModelPK(List<String> modelCodeList){
        BaseResponse modelEtlPKRb = restUtil.getModelEtlPK(modelCodeList);


        if (!modelEtlPKRb.checkIsSuccess() || Objects.isNull(modelEtlPKRb.getData())) {
            return new HashMap<>();
        }

        List<Map<String, Object>> data = (List<Map<String, Object>>) modelEtlPKRb.getData();

        Map<String, String> modelPkMap = data.stream().collect(Collectors.toMap(i -> StringUtil.toString(i.get("collectCode")), i -> StringUtil.toString(i.get("sinkPk")), (existingValue, newValue) -> existingValue));
        return modelPkMap;
    }


    private boolean isSpecialProjectType(String categoryCode) {
        return NETWORK_SECURITY_EXAM_PORJECT_ORGANIZATIONAL.equals(categoryCode) ||
                NETWORK_SECURITY_EXAM_PORJECT_LOCALIZATION.equals(categoryCode);
    }

    // 处理特殊项目类型
    private void processSpecialProjectType(NetworkSecurityExaminationProjectType projectType, Long eid) {
        String sql = String.format("SELECT %s FROM %s WHERE eid = %s LIMIT 1",
                projectType.getModelPk(), MODEL_DB + projectType.getModelCode(), eid);

        List<Map<String, Object>> maps = bigDataUtil.srQuery(sql);
        if (!CollectionUtils.isEmpty(maps)) {
            projectType.setModelPkValue(
                    maps.get(0).get(projectType.getModelPk()).toString()
            );
        }
    }

    private Map<String, Object> querySelectedCount(List<NetworkSecurityExaminationProjectType> nseptList, Long aerId) {
        Map<String, Object> modelCountMap = new HashMap<>();
        List<AiopsExamItemInstanceScore> instanceScoreList = aiopsExamRecordMapper
                .selectScoresByAerIdListOrNetworkExamCategoryCode(Stream.of(aerId).collect(Collectors.toList()),
                        null);

        Map<String, Long> categoryCodeCountMap = instanceScoreList.stream()
                .filter(score -> score.getNetworkExamCategoryCode() != null)
                .collect(Collectors.groupingBy(
                        AiopsExamItemInstanceScore::getNetworkExamCategoryCode,
                        Collectors.counting()
                ));
        for (NetworkSecurityExaminationProjectType networkSecurityExaminationProjectType : nseptList) {
            modelCountMap.put(networkSecurityExaminationProjectType.getModelCode(),
                    categoryCodeCountMap.getOrDefault(networkSecurityExaminationProjectType.getCategoryCode(), 0L));
        }

        return modelCountMap;

    }


    /**
     * 构建动态SQL查询
     *
     * @param modelCodeList 模型代码列表
     * @param selectClause  SELECT子句内容，如 "COUNT(1) cnt" 或 "*"
     * @param groupByClause GROUP BY子句，可为null
     * @param whereClause   WHERE子句，可为null
     * @return 构建的SQL语句
     */
    private String buildUnionSql(List<String> modelCodeList, String selectClause, String groupByClause, String whereClause) {
        if (CollectionUtils.isEmpty(modelCodeList)) {
            return "";
        }

        StringBuilder sqlBuilder = new StringBuilder();
        for (int i = 0; i < modelCodeList.size(); i++) {
            String modelCode = modelCodeList.get(i);
            sqlBuilder.append("SELECT ").append(selectClause).append(", '")
                    .append(modelCode).append("' AS modelCode FROM ")
                    .append(MODEL_DB)
                    .append(modelCode);

            if (StringUtils.isNotBlank(whereClause)) {
                sqlBuilder.append(" WHERE ").append(whereClause);
            }

            if (i < modelCodeList.size() - 1) {
                sqlBuilder.append(" UNION ALL ");
            } else if (StringUtils.isNotBlank(groupByClause)) {
                sqlBuilder.append(" ").append(groupByClause);
            }
        }
        return sqlBuilder.toString();
    }

    // 使用示例 - 查询计数
    private List<Map<String, Object>> queryModelCount(List<String> modelCodeList,Long eid) {
        if (CollectionUtils.isEmpty(modelCodeList)) {
            return Collections.emptyList();
        }
        String sql = buildUnionSql(modelCodeList, "COUNT(1) cnt", "GROUP BY modelCode", " isDeleted = 0 and eid = " + eid);
        return bigDataUtil.srQuery(sql);
    }

    // 使用示例 - 查询明细
    private List<Map<String, Object>> queryModelDetail(List<String> modelCodeList, String whereClause) {
        if (CollectionUtils.isEmpty(modelCodeList)) {
            return Collections.emptyList();
        }
        String sql = buildUnionSql(modelCodeList, "*", null, whereClause);
        return bigDataUtil.srQuery(sql);
    }

    private List<String> getModelCodeListFromProjectTypes(List<NetworkSecurityExaminationProjectType> nseptList) {
        return nseptList.stream()
                .map(NetworkSecurityExaminationProjectType::getModelCode)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());

    }

    private List<NetworkSecurityExaminationProjectType> getProjectTypeList(String categoryCode, String categoryName, String parentCode, Boolean filterNullModel) {
        return networkSecurityExamMapper
                .selectProjectType(categoryCode, categoryName, parentCode, filterNullModel, null);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResponseBase saveNetworkSecurityAsset(List<AiopsExamItemInstanceScore> aeiisList, Long recordId) {
        // 检查参数列表是否为空
        if (CollectionUtils.isEmpty(aeiisList)) {
            return ResponseBase.error(PARAM_IS_EMPTY.getCode(), String.format(PARAM_IS_EMPTY.getMsg(), "aeiisList"));
        }

        // 检查网络安全类别代码是否为空
        if (aeiisList.stream().anyMatch(i -> StringUtils.isEmpty(i.getNetworkExamCategoryCode()))) {
            return ResponseBase.error(PARAM_IS_EMPTY.getCode(), String.format(PARAM_IS_EMPTY.getMsg(), "networkExamCategoryCode"));
        }

        if (aeiisList.stream().anyMatch(i -> StringUtils.isEmpty(i.getAiopsItemId()))) {
            return ResponseBase.error(PARAM_IS_EMPTY.getCode(), String.format(PARAM_IS_EMPTY.getMsg(), AIOPS_ITEMID));
        }

        if (aeiisList.stream().anyMatch(i -> LongUtil.isEmpty(i.getNetworkExamAssetId()))) {
            return ResponseBase.error(PARAM_IS_EMPTY.getCode(), String.format(PARAM_IS_EMPTY.getMsg(), "networkExamAssetId"));
        }

        List<String> aiopsItemIdList = aeiisList.stream()
                .map(AiopsExamItemInstanceScore::getAiopsItemId)
                .collect(Collectors.toList());

        // 批量查询实例信息
        List<AiopsInstance> aiopsInstanceList = instanceMapper.selectAiopsInstanceByAiopsItemIdList(aiopsItemIdList);

        // 创建ID到项目的映射
        Map<String, String> aiopsItemIdMap = aiopsInstanceList.stream()
                .collect(Collectors.toMap(
                        AiopsInstance::getAiopsItemId,
                        AiopsInstance::getAiopsItem,
                        (existing, replacement) -> replacement
                ));

        for (AiopsExamItemInstanceScore score : aeiisList) {
            // 如果项目名称为空，则从映射中获取
            if (StringUtils.isEmpty(score.getAiopsItem())) {
                score.setAiopsItem(aiopsItemIdMap.get(score.getAiopsItemId()));
            }
            if (LongUtil.isEmpty(score.getNetworkExamAssetId())) {
                throw new RuntimeException("networkExamAssetId is empty");
            }
            aiopsExamService.saveAiopsExamItemInstanceScore(score, recordId);
        }

        return ResponseBase.ok();
    }

    @Override
    public BaseResponse getNetworkSecurityExamAsset(NetworkSecurityExamAssetRequest request) {
        Optional<BaseResponse> optResponse = checkParamIsEmpty(request.getTableName(), "tableName");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        optResponse = checkParamIsEmpty(request.getAssetCategoryCode(), "assetCategoryCode");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        optResponse = checkParamIsEmpty(request.getRecordId(), "recordId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        ResponseBase<List<NetworkSecurityExaminationProjectType>> projectTypeRb =
                getProjectType(request.getAssetCategoryCode(), null, null, true, request.getRecordId(), null);

        if (!projectTypeRb.checkIsSuccess() || (projectTypeRb.checkIsSuccess() && CollectionUtils.isEmpty(projectTypeRb.getData()))) {
            log.error("[getNetworkSecurityExamAsset] getProjectType error: {}", projectTypeRb);
            return BaseResponse.ok();
        }

        NetworkSecurityExaminationProjectType networkSecurityExaminationProjectType = projectTypeRb.getData().get(0);
        String modelPk = networkSecurityExaminationProjectType.getModelPk();

        List<AiopsExamItemInstanceScore> instanceList = aiopsExamRecordMapper
                .selectScoresByAerIdListOrNetworkExamCategoryCode(
                        Collections.singletonList(request.getRecordId()),
                        request.getAssetCategoryCode());

        // 如果没有数据，直接返回空列表
        if (instanceList.isEmpty()) {
            return BaseResponse.ok(new PageInfo<>(new ArrayList<>()));
        }

        // 获取项目ID列表
        List<String> aiopsItemIdList = instanceList.stream()
                .map(AiopsExamItemInstanceScore::getAiopsItemId)
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.toList());

        List<String> idList = instanceList.stream()
                .map(AiopsExamItemInstanceScore::getNetworkExamAssetId)
                .filter(LongUtil::isNotEmpty)
                .map(LongUtil::safeToString)
                .collect(Collectors.toList());

        if (idList.isEmpty()) {
            return BaseResponse.ok(new PageInfo<>(new ArrayList<>()));
        }

        // 查询数据
        if (request.isNeedPaging()) {
            request.setPageIndex(request.getPageIndex());
            request.setPageSize(request.getPageSize());
        } else {
            request.setPageIndex(null);
            request.setPageSize(null);
        }
        Query query = new Query();
        BeanUtils.copyProperties(request, query);
        List<Query.BusinessCondition> businessCondition = query.getBusinessCondition();
        businessCondition.add(new Query.BusinessCondition(AIOPS_ITEMID, "VARCHAR", "IN", String.join(",", aiopsItemIdList)));
        businessCondition.add(new Query.BusinessCondition(modelPk, "BIGINT", "IN", String.join(",", idList)));
        ResponseBase responseBase = apiController.commonQuery(query);

        // 构建返回结果
        BaseResponse ok = BaseResponse.ok();
        BeanUtils.copyProperties(responseBase, ok);
        return ok;
    }

    @Override
    public BaseResponse deleteNetworkSecurityExamAsset(String aiopsItemId, Long recordId) {
        // 参数校验
        Optional<BaseResponse> optResponse = checkParamIsEmpty(aiopsItemId, AIOPS_ITEMID);
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        optResponse = checkParamIsEmpty(recordId, "recordId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        // 执行删除操作
        int deletedCount = networkSecurityExamMapper.deleteByAiopsItemIdAndAerId(aiopsItemId, recordId);

        if (deletedCount > 0) {
            return BaseResponse.ok();
        } else {
            return BaseResponse.ok("没有找到符合条件的记录");
        }
    }

    @Override
    public BaseResponse saveNetworkSecurityExamReport(AiopsExamRecordsReportRecord reportRecord) {
        BaseResponse<AiopsExamRecordsReportRecord> baseResponse = aiopsExamService.insertAiopsExamRecordsReportRecord(reportRecord);
        if (!baseResponse.checkIsSuccess()) {
            return baseResponse;
        }
        AiopsExamRecordsReportRecord data = baseResponse.getData();
        CompletableFuture.runAsync(() -> saveNetworkSecurityExamReport2Es(data));
        return baseResponse;
    }

    @Override
    public BaseResponse getNetworkSecurityExamReport(Long reportRecordId, int scale) {
        AiopsExamRecordsReportRecord aiopsExamRecordsReportRecord = aiopsExamRecordMapper.selectAiopsExamRecordsReportRecord(reportRecordId);
        if (Objects.isNull(aiopsExamRecordsReportRecord)) {
            return BaseResponse.ok();
        }
        AiopsExamRecordsEsReportRecord esReportReport = networkSecurityExamEsReport.getReport(LongUtil.safeToString(reportRecordId));
        aiopsExamRecordsReportRecord.setOrganizationalData(esReportReport.getOrganizationalData());
        aiopsExamRecordsReportRecord.setWarningData(esReportReport.getWarningData());

        BaseResponse<AiopsExamRecordsReportRecord> reportScoreBr = getReportScore(aiopsExamRecordsReportRecord.getAerId(), scale);
        if (!reportScoreBr.checkIsSuccess()) {
            log.error("[getNetworkSecurityExamReport] error , {}", reportScoreBr);
        }
        AiopsExamRecordsReportRecord scoreData = reportScoreBr.getData();
        aiopsExamRecordsReportRecord.setExamScore(scoreData.getExamScore());
        aiopsExamRecordsReportRecord.setIndexTypeList(scoreData.getIndexTypeList());
        aiopsExamRecordsReportRecord.setExamTitle(scoreData.getExamTitle());
        aiopsExamRecordsReportRecord.setExamEnv(scoreData.getExamEnv());
        return BaseResponse.ok(aiopsExamRecordsReportRecord);
    }


    @Override
    public BaseResponse getAiopsItem(String modelCode, Long eid) {
        String projectCategoryCode = getProjectCategoryCode(modelCode);
        return BaseResponse.ok(getAiopsItemList(projectCategoryCode, eid));
    }

    @Override
    public BaseResponse getProjectInstance(String aiopsItem, Long eid) {
        Map<String, Object> param = new HashMap<>(3);
        param.put("aiopsItemList", Lists.newArrayList(aiopsItem));
        param.put("eid", eid);
        param.put("aiopsAuthStatusList", INSTANCE_STATUS_LIST);

        List<MergeStatisticsDetailInfo> mergeStatisticsDetailInfos =
                instanceMapper.selectMergeStatisticsDetailByMap(param);
        List<MergeStatisticsDetailInfo> finalInstanceList = mergeStatisticsDetailInfos.stream().filter(i -> !CollectionUtils.isEmpty(i.getAiopsKitDeviceList()))
                .filter(i -> i.getAiopsKitDeviceList().stream().anyMatch(d -> !d.getIsDeleted()))
                .collect(Collectors.toList());
        Map<String, List<MergeStatisticsDetailInfo>> aiopsItemMap =
                finalInstanceList.stream().collect(Collectors.groupingBy(MergeStatisticsDetailInfo::getAiopsItem));
        if (aiopsItemMap.isEmpty()) {
            return BaseResponse.ok(new ArrayList<>());
        }

        fillAddDeviceModel(aiopsItemMap);
        fillAddDatabaseExtendInfo(aiopsItemMap);

        List<AiopsItem> aiopsItemsList = instanceMapper.selectAiopsItemByCodeList(aiopsItemMap.keySet());

        List<AiopsItemExtend> aiopsItemExtendList = aiopsItemsList.stream().map(ai -> {
            AiopsItemExtend aiopsItemExtend = new AiopsItemExtend();
            BeanUtils.copyProperties(ai, aiopsItemExtend);
            aiopsItemExtend.setAiopsInstanceList(aiopsItemMap.get(ai.getCode()));
            return aiopsItemExtend;
        }).collect(Collectors.toList());
        return BaseResponse.ok(aiopsItemExtendList);
    }
    /**
     * 为HOST和CLIENT类型的设备填充deviceModel字段
     */
    private void fillAddDeviceModel(Map<String, List<MergeStatisticsDetailInfo>> aiopsItemMap) {
        // 只处理HOST和CLIENT类型
        Set<String> targetTypes = new HashSet<>();
        targetTypes.add("HOST");
        targetTypes.add("CLIENT");

        // 收集所有HOST和CLIENT类型的aiopsItemId
        List<String> allDeviceIds = aiopsItemMap.entrySet().stream()
                .filter(e -> targetTypes.contains(e.getKey()))
                .flatMap(e -> e.getValue().stream())
                .map(MergeStatisticsDetailInfo::getAiopsItemId)
                .filter(StringUtil::isNotEmpty)
                .distinct()
                .collect(Collectors.toList());

        if (allDeviceIds.isEmpty()) {
            return;
        }

        // 查询设备的bios_family信息
        String inClause = allDeviceIds.stream()
                .map(id -> "'" + id.replace("'", "''") + "'")
                .collect(Collectors.joining(","));
        String sql = String.format(
                "SELECT DISTINCT deviceId, bios_family FROM servicecloud.DeviceAdvancedInfoAttributeModel WHERE deviceId IN (%s)",
                inClause
        );
        List<Map<String, Object>> queryResult = bigDataUtil.srQuery(sql);

        // 构建deviceId到bios_family的映射
        Map<String, String> deviceModelMap = queryResult.stream()
                .collect(Collectors.toMap(
                        m -> StringUtil.toString(m.get("deviceId")),
                        m -> StringUtil.toString(m.getOrDefault("bios_family", "")),
                        (oldVal, newVal) -> oldVal
                ));

        // 填充deviceModel字段
        aiopsItemMap.entrySet().stream()
                .filter(e -> targetTypes.contains(e.getKey()))
                .flatMap(e -> e.getValue().stream())
                .forEach(item -> item.setDeviceModel(deviceModelMap.getOrDefault(item.getAiopsItemId(), "")));
    }

    private void fillAddDatabaseExtendInfo(Map<String, List<MergeStatisticsDetailInfo>> aiopsItemMap) {
        // 只处理MSSQL和ORACLE类型
        Set<String> targetTypes = new HashSet<>();
        targetTypes.add("MSSQL");
        targetTypes.add("ORACLE");

        // 收集所有MSSQL和ORACLE类型的aiopsInstanceId（source_db_id，Long转String）
        List<String> mssqlIds = aiopsItemMap.getOrDefault("MSSQL", Collections.emptyList())
                .stream().map(item -> item.getAiopsItemId() == null ? null : item.getAiopsItemId())
                .filter(StringUtil::isNotEmpty).distinct().collect(Collectors.toList());
        List<String> oracleIds = aiopsItemMap.getOrDefault("ORACLE", Collections.emptyList())
                .stream().map(item -> item.getAiopsItemId() == null ? null : item.getAiopsItemId())
                .filter(StringUtil::isNotEmpty).distinct().collect(Collectors.toList());

        // 查询 MSSQL 版本
        final Map<String, String> mssqlVersionMap;
        if (!mssqlIds.isEmpty()) {
            String inClause = mssqlIds.stream().map(id -> "'" + id.replace("'", "''") + "'").collect(Collectors.joining(","));
            String sql = "select t1.sqlserver_version, t1.source_db_id from servicecloud.SQLServerDatabaseVersion t1 " +
                    "inner join (select max(collectedTime) collectedTime, source_db_id from servicecloud.SQLServerDatabaseVersion group by source_db_id) t2 " +
                    "on t1.collectedTime = t2.collectedTime and t1.source_db_id = t2.source_db_id " +
                    "where t1.source_db_id in (" + inClause + ")";
            List<Map<String, Object>> result = bigDataUtil.srQuery(sql);
            mssqlVersionMap = result.stream().collect(Collectors.toMap(
                    m -> StringUtil.toString(m.get("source_db_id")),
                    m -> StringUtil.toString(m.getOrDefault("sqlserver_version", "")),
                    (o, n) -> o
            ));
        } else {
            mssqlVersionMap = Collections.emptyMap();
        }
        // 查询 MSSQL 大小
        final Map<String, String> mssqlSizeMap;
        if (!mssqlIds.isEmpty()) {
            String inClause = mssqlIds.stream().map(id -> "'" + id.replace("'", "''") + "'").collect(Collectors.joining(","));
            String sql = "select sum(replace(database_size,'MB','')) as database_size, source_db_id from servicecloud.MsSQLDbSize " +
                    "where database_name not in('master','model','msdb','tempdb','ReportServer','ReportSeerverTempDB') " +
                    "and source_db_id in (" + inClause + ") group by source_db_id";
            List<Map<String, Object>> result = bigDataUtil.srQuery(sql);
            mssqlSizeMap = result.stream().collect(Collectors.toMap(
                    m -> StringUtil.toString(m.get("source_db_id")),
                    m -> StringUtil.toString(m.getOrDefault("database_size", "")),
                    (o, n) -> o
            ));
        } else {
            mssqlSizeMap = Collections.emptyMap();
        }
        // 查询 ORACLE 版本
        final Map<String, String> oracleVersionMap;
        if (!oracleIds.isEmpty()) {
            String inClause = oracleIds.stream().map(id -> "'" + id.replace("'", "''") + "'").collect(Collectors.joining(","));
            String sql = "select t1.ora_version, t1.source_db_id from servicecloud.T100OracleDatabaseBasicInformation_sr_duplicate t1 " +
                    "inner join (select max(collectedTime) collectedTime, source_db_id from servicecloud.T100OracleDatabaseBasicInformation_sr_duplicate group by source_db_id) t2 " +
                    "on t1.collectedTime = t2.collectedTime and t1.source_db_id = t2.source_db_id " +
                    "where t1.source_db_id in (" + inClause + ")";
            List<Map<String, Object>> result = bigDataUtil.srQuery(sql);
            oracleVersionMap = result.stream().collect(Collectors.toMap(
                    m -> StringUtil.toString(m.get("source_db_id")),
                    m -> StringUtil.toString(m.getOrDefault("ora_version", "")),
                    (o, n) -> o
            ));
        } else {
            oracleVersionMap = Collections.emptyMap();
        }
        // 查询 ORACLE 大小
        final Map<String, String> oracleSizeMap;
        if (!oracleIds.isEmpty()) {
            String inClause = oracleIds.stream().map(id -> "'" + id.replace("'", "''") + "'").collect(Collectors.joining(","));
            String sql = "select sum(sum_MB) as database_size, otsc1.source_db_id from servicecloud.OracleDBTableSpaceCollected_sr_duplicate otsc1 " +
                    "inner join (select max(collectedTime) collectedTime, source_db_id from servicecloud.OracleDBTableSpaceCollected_sr_duplicate group by source_db_id) otsc2 " +
                    "on otsc1.collectedTime = otsc2.collectedTime and otsc1.source_db_id = otsc2.source_db_id " +
                    "where otsc1.source_db_id in (" + inClause + ") group by otsc1.source_db_id";
            List<Map<String, Object>> result = bigDataUtil.srQuery(sql);
            oracleSizeMap = result.stream().collect(Collectors.toMap(
                    m -> StringUtil.toString(m.get("source_db_id")),
                    m -> StringUtil.toString(m.getOrDefault("database_size", "")),
                    (o, n) -> o
            ));
        } else {
            oracleSizeMap = Collections.emptyMap();
        }
        // 填充 MSSQL
        aiopsItemMap.getOrDefault("MSSQL", Collections.emptyList()).forEach(item -> {
            String id = item.getAiopsItemId() == null ? null : item.getAiopsItemId();
            item.setDatabaseVersion(mssqlVersionMap.getOrDefault(id, ""));
            item.setDatabaseSize(mssqlSizeMap.getOrDefault(id, ""));
        });
        // 填充 ORACLE
        aiopsItemMap.getOrDefault("ORACLE", Collections.emptyList()).forEach(item -> {
            String id = item.getAiopsItemId() == null ? null : item.getAiopsItemId();
            item.setDatabaseVersion(oracleVersionMap.getOrDefault(id, ""));
            item.setDatabaseSize(oracleSizeMap.getOrDefault(id, ""));
        });
    }

    private String getProjectCategoryCode(String modelCode) {
        List<NetworkSecurityExaminationProjectType> networkSecurityExaminationProjectTypeList
                = networkSecurityExamMapper.selectProjectType(null, null, null,
                true, modelCode);

        return networkSecurityExaminationProjectTypeList.get(0).getCategoryCode();

    }

    private List<AiopsItemExtend> getAiopsItemList(String projectCategoryCode, Long eid) {
        switch (projectCategoryCode) {
            case "Device":
                return instanceMapper.selectAiopsItemByGroupCode(eid, null, Sets.newHashSet(
                        "HOST", "CLIENT", "NAS", "FIREWALL",
                        "ESXI", "SWITCH", "UPS", "RAID",
                        "SNMP", "iLO", "iDRAC", "iMM",
                        "XCC", "MAC", "CRH", "TMP_RH"), INSTANCE_STATUS_LIST);
            case "Database":
                return instanceMapper.selectAiopsItemByGroupCode(eid, "DATABASE", null, INSTANCE_STATUS_LIST);
            case "InfoSystem":
                List<AiopsItemExtend> productApp = instanceMapper.selectAiopsItemByGroupCode(eid, "PRODUCT_APP", null, INSTANCE_STATUS_LIST);

                return productApp;
            default:
                return new ArrayList<>();
        }
    }


    private void saveNetworkSecurityExamReport2Es(AiopsExamRecordsReportRecord record) {
        try {
            String appCode = RequestUtil.getHeaderAppCode();
            Long eid = record.getEid();

            //查询组织类
            ResponseBase<List<NetworkSecurityExaminationProjectType>> projectType =
                    getProjectType(NETWORK_SECURITY_EXAM_PORJECT_ORGANIZATIONAL, null, null, true, null, eid);

            if (!projectType.checkIsSuccess()) {
                log.error("[saveNetworkSecurityExamReport2Es] error , {}", projectType);
                return;
            }

            NetworkSecurityExaminationProjectType organizationalCodeType = projectType.getData().get(0);
            // 获取模型代码列表
            List<String> modelCodeList = getModelCodeListFromProjectTypes(Stream.of(organizationalCodeType).collect(Collectors.toList()));

            //存储es
            List<Map<String, Object>> organizationalDataList = queryModelDetail(modelCodeList, organizationalCodeType.getModelPk() + "=" + organizationalCodeType.getModelPkValue());

            if (organizationalDataList.isEmpty()) {
                return;
            }
            //只会有一条数据
            Map<String, Object> organizationalData = organizationalDataList.get(0);
            //只会存在一个模型
            BaseResponse<ModelDetail> modelDetailBr = restUtil.getModelDetail(modelCodeList.get(0));
            if (Objects.nonNull(modelDetailBr.getData())) {
                doModelData(organizationalData, modelDetailBr.getData(), record);
            }
            AiopsExamRecord aiopsExamRecord = aiopsExamRecordMapper.selectAiopsExamRecordReport(record.getAerId());
            LocalDateTime examStartTime = aiopsExamRecord.getExamStartTime();
            List<Map<String, Object>> warningData = warningService.getDailyWarningInfo(appCode, eid, examStartTime.toLocalDate().plusDays(-30),
                    examStartTime.toLocalDate());
            record.setWarningData(warningData);
            //更新报告状态
            record.setGenerationTime(LocalDateTime.now());
            networkSecurityExamEsReport.generateReport(record);
            aiopsExamRecordMapper.updateReportStatus(record.getId(), ReportStatus.UNDER_EVA.getIndex(), LocalDateTime.now());
        } catch (Exception e) {
            log.error("[saveNetworkSecurityExamReport2Es] error,{}", e.getMessage(), e);
        }

    }

    private void doModelData(Map<String, Object> map, ModelDetail modelDetail, AiopsExamRecordsReportRecord record) {

        OrganizationalModel model = new OrganizationalModel();
        List<OrganizationalModel.OrganizationalModelMap> organizationalModelMapList = new ArrayList<>();
        List<FieldTypeEnum> fieldTypeEnumList = new ArrayList<>();
        List<ModelFieldMapping> modelFieldMappingList = modelDetail.getModelFieldGroupList().stream().flatMap(i -> i.getModelFieldMappingList().stream())
                .collect(Collectors.toList());
        Map<String, Integer> fieldSortMap = modelFieldMappingList.stream().collect(Collectors.toMap(i -> i.getTargetCode().toLowerCase(), ModelFieldMapping::getSort, (o, n) -> n));

        // 处理模型字段组
        for (ModelFieldGroup modelFieldGroup : modelDetail.getModelFieldGroupList()) {
            OrganizationalModel.OrganizationalModelMap organizationalModelMap = new OrganizationalModel.OrganizationalModelMap();
            organizationalModelMap.setGroup(modelFieldGroup.getModelFieldGroupCode());
            List<Map<String, String>> dataList = new ArrayList<>();

            // 处理字段映射
            for (ModelFieldMapping modelFieldMapping : modelFieldGroup.getModelFieldMappingList()) {
                Field field = modelFieldMapping.getField();
                FieldSet fieldSet = modelFieldMapping.getFieldSet();

                if (Objects.nonNull(fieldSet)) {
                    // 处理字段集合
                    List<Field> fieldSetMappings = fieldSet.getFieldList();
                    if (!CollectionUtils.isEmpty(fieldSetMappings)) {
                        for (Field subfield : fieldSetMappings) {
                            Map<String, String> subDataMap = new HashMap<>();
                            subDataMap.put("name", subfield.getFieldName());
                            subDataMap.put("fieldSetName", fieldSet.getFieldSetName());
                            subDataMap.put("fieldSetCode", fieldSet.getFieldSetCode().toLowerCase());
                            subDataMap.put("code", subfield.getFieldCode());
                            subDataMap.put("value", StringUtil.toString(map.get(subfield.getFieldCode())));
                            subDataMap.put("sourceName", subfield.getFieldName());
                            subDataMap.put("sort", StringUtil.toString(fieldSortMap.get(fieldSet.getFieldSetCode().toLowerCase())));
                            // 添加枚举类型
                            if (!CollectionUtils.isEmpty(subfield.getFieldTypeEnumList())) {
                                subDataMap.put("fieldTypeEnumList", JSONObject.toJSONString(subfield.getFieldTypeEnumList()));
                                //如果分组类别是 Mechanisms 并且是枚举类型，则写死name为字段集名称
                                subDataMap.put("name", fieldSet.getFieldSetName());

                            }
                            dataList.add(subDataMap);
                        }
                    }
                } else if (Objects.nonNull(field)) {
                    // 处理单个字段
                    Map<String, String> dataMap = new HashMap<>();
                    dataMap.put("name", field.getFieldName());
                    dataMap.put("fieldSetName", "");
                    dataMap.put("code", field.getFieldCode());
                    dataMap.put("value", StringUtil.toString(map.get(field.getFieldCode())));
                    dataMap.put("sort", StringUtil.toString(fieldSortMap.get(field.getFieldCode())));
                    // 添加枚举类型
                    if (!CollectionUtils.isEmpty(field.getFieldTypeEnumList())) {
                        dataMap.put("fieldTypeEnumList", JSONObject.toJSONString(field.getFieldTypeEnumList()));
                    }

                    dataList.add(dataMap);
                }
            }

            if (DATA_CONTENT.equals(modelFieldGroup.getModelFieldGroupCode())) {
                // 分组处理带有fieldSetName的数据
                Map<String, List<Map<String, String>>> fieldSetNameMap = dataList.stream()
                        .filter(i -> StringUtils.isNotBlank(i.get("fieldSetName")))
                        .collect(Collectors.groupingBy(i -> i.get("fieldSetName")));

                // 处理没有fieldSetName的数据
                List<Map<String, String>> fieldList = dataList.stream()
                        .filter(i -> StringUtils.isEmpty(i.get("fieldSetName")))
                        .collect(Collectors.toList());

                // 处理带有fieldSetName的数据
                List<OrganizationalModel.OrganizationalModelMapDataContent> resDataList = fieldSetNameMap.entrySet().stream()
                        .map(entry -> createDataContent(
                                entry.getKey(),
                                entry.getValue(),
                                () -> getSortFromList(entry.getValue())
                        ))
                        .collect(Collectors.toList());

                // 处理独立字段数据
                List<OrganizationalModel.OrganizationalModelMapDataContent> fieldDataList = fieldList.stream()
                        .map(field -> createDataContent(
                                "",
                                Collections.singletonList(field),
                                () -> Optional.ofNullable(field.get("sort"))
                                        .map(Integer::parseInt)
                                        .orElse(0)
                        ))
                        .collect(Collectors.toList());

                // 合并并排序所有数据
                resDataList.addAll(fieldDataList);
                resDataList = resDataList.stream()
                        .sorted(Comparator.comparingInt(OrganizationalModel.OrganizationalModelMapDataContent::getSort))
                        .collect(Collectors.toList());

                organizationalModelMap.setDataContentList(mergeAdjacentElements(resDataList));
                organizationalModelMapList.add(organizationalModelMap);
            } else {
                organizationalModelMap.setDataList(dataList);
                organizationalModelMapList.add(organizationalModelMap);
            }
        }

        model.setFieldTypeEnumList(fieldTypeEnumList);
        model.setOrganizationalModelMapList(organizationalModelMapList);
        record.setOrganizationalData(model);
    }

    // 辅助方法：创建数据内容对象
    private OrganizationalModel.OrganizationalModelMapDataContent createDataContent(
            String fieldSetName,
            List<Map<String, String>> dataList,
            Supplier<Integer> sortSupplier) {
        OrganizationalModel.OrganizationalModelMapDataContent content = new OrganizationalModel.OrganizationalModelMapDataContent();
        content.setFieldSetName(fieldSetName);
        content.setDataContentList(dataList);
        content.setSort(sortSupplier.get());
        return content;
    }
    // 辅助方法：从列表中获取排序值
    private Integer getSortFromList(List<Map<String, String>> list) {
        return Optional.ofNullable(list)
                .filter(l -> !l.isEmpty())
                .map(l -> l.get(0))
                .map(m -> m.get("sort"))
                .map(s -> {
                    try {
                        return Integer.parseInt(s);
                    } catch (NumberFormatException e) {
                        log.warn("Failed to parse sort value: {}", s);
                        return 0;
                    }
                })
                .orElse(0);
    }

    public List<OrganizationalModel.OrganizationalModelMapDataContent> mergeAdjacentElements(List<OrganizationalModel.OrganizationalModelMapDataContent> resDataList) {
        List<OrganizationalModel.OrganizationalModelMapDataContent> result = new ArrayList<>();
        if (resDataList == null || resDataList.isEmpty()) {
            return result;
        }

        OrganizationalModel.OrganizationalModelMapDataContent currentGroup = null;

        for (OrganizationalModel.OrganizationalModelMapDataContent element : resDataList) {
            if (currentGroup == null) {
                // 初始化当前合并组
                currentGroup = createNewGroup(element);
            } else {
                if (currentGroup.getFieldSetName().equals(element.getFieldSetName())) {
                    // 合并dataContentList
                    List<Map<String, String>> elementData = Optional.ofNullable(element.getDataContentList()).orElseGet(ArrayList::new);
                    currentGroup.getDataContentList().addAll(elementData);
                } else {
                    // 添加到结果，并创建新组
                    result.add(currentGroup);
                    currentGroup = createNewGroup(element);
                }
            }
        }

        // 添加最后一个合并组
        result.add(currentGroup);

        return result;
    }

    private OrganizationalModel.OrganizationalModelMapDataContent createNewGroup(OrganizationalModel.OrganizationalModelMapDataContent element) {
        OrganizationalModel.OrganizationalModelMapDataContent group = new OrganizationalModel.OrganizationalModelMapDataContent();
        group.setFieldSetName(element.getFieldSetName());
        group.setSort(element.getSort());
        // 处理可能的null，创建新的dataContentList
        List<Map<String, String>> dataContent = Optional.ofNullable(element.getDataContentList())
                .map(ArrayList::new)
                .orElseGet(ArrayList::new);
        group.setDataContentList(dataContent);
        return group;
    }
    private BaseResponse<AiopsExamRecordsReportRecord> getReportScore(Long aerId, int scale) {

        AiopsExamRecord aer = aiopsExamRecordMapper.selectAiopsExamRecordReport(aerId);
        if (aer == null) {
            return BaseResponse.error(AIOPS_EXAM_ERROR);
        }

        AiopsExam exam = aiopsExamService.getAeById(aer.getAeId());
        if (exam == null) {
            return BaseResponse.error(AIOPS_EXAM_ERROR);
        }

        List<AiopsExamIndexType> indexTypes = aiopsExamMapper.selectIndexTypeByAeid(aer.getAeId());

        List<String> aiopsItemList = aiopsExamRecordMapper.selectAiopsExamItem(aerId).stream().map(AiopsExamItemMap::getAiopsItem)
                .collect(Collectors.toList());

        List<AiopsExamItemMap> enabledMaps = exam.getAeimList().stream()
                .filter(e -> aiopsItemList.contains(e.getAiopsItem()))
                .collect(Collectors.toList());

        Map<Long, AiopsExamIndex> indexMap = enabledMaps.stream()
                .flatMap(map -> map.getAeiList().stream())
                .collect(Collectors.toMap(AiopsExamIndex::getId, Function.identity()));

        Map<Long, AiopsExamItemIndexTypeScore> aeiitsMap = aer.getAeiitsList()
                .stream()
                .collect(Collectors.toMap(AiopsExamItemIndexTypeScore::getAeitId, Function.identity(), (o, n) -> o));

        Map<Long, AiopsExamIndexScore> aeisMap = aer.getAeIndexList().stream()
                .collect(Collectors.toMap(AiopsExamIndexScore::getAeiId, Function.identity(), (o, n) -> o));

        processAeitList(indexTypes, indexMap, aeiitsMap, aeisMap, scale);
        AiopsExamRecordsReportRecord reportRecord = new AiopsExamRecordsReportRecord();
        reportRecord.setExamScore(aer.getExamScore().setScale(scale, RoundingMode.HALF_UP));
        reportRecord.setIndexTypeList(indexTypes);
        reportRecord.setExamEnv(aer.getExamEnv());
        reportRecord.setExamTitle(aer.getExamTitle());
        return BaseResponse.okT(reportRecord);
    }

    private void processAeitList(List<AiopsExamIndexType> aeitList,
                                 Map<Long, AiopsExamIndex> indexMap,
                                 Map<Long, AiopsExamItemIndexTypeScore> aeitMap,
                                 Map<Long, AiopsExamIndexScore> aeisMap,
                                 int scale) {
        for (AiopsExamIndexType aeit : aeitList) {
            List<Long> aeiIdList = aeit.getAeiList().stream().map(AiopsExamIndex::getId).collect(Collectors.toList());

            AiopsExamItemIndexTypeScore aeiits = aeitMap.get(aeit.getId());

            if (aeiits == null) {
                log.error(" [processAeitList]: AiopsExamItemIndexTypeScore empty aeitId:{} , aeitCode:{}", aeit.getId(), aeit.getCode());
                continue;
            }
            // 分类分数四舍五入
            aeiits.setExamScore(aeiits.getExamScore().setScale(scale, RoundingMode.HALF_UP));
            List<AiopsExamIndex> aeiList = new ArrayList<>();
            for (Long aeiId : aeiIdList) {
                AiopsExamIndex aiopsExamIndex = indexMap.get(aeiId);
                AiopsExamIndexScore aiopsExamIndexScore = aeisMap.get(aeiId);
                if (Objects.isNull(aiopsExamIndexScore)) {
                    continue;
                }
                aiopsExamIndexScore.setExamScore(aiopsExamIndexScore.getExamScore().setScale(scale, RoundingMode.HALF_UP));
                aiopsExamIndex.setAeis(aiopsExamIndexScore);
                aeiList.add(aiopsExamIndex);

            }

            aeit.setAeiits(aeiits);
            aeit.setAeiList(aeiList);
        }
    }

    @Override
    public ResponseBase<Long> getNetworkSecurityAe() {
        Long examId = networkSecurityExamMapper.selectExamIdByCode("Network_Security_Examination");
        if (examId == null) {
            return ResponseBase.error(AIOPS_EXAM_ERROR);
        }
        return ResponseBase.okT(examId);
    }

    @Override
    public ResponseBase getEnableAeim(){
        ResponseBase<Long> ae = getNetworkSecurityAe();
        if (!ae.checkIsSuccess()) {
            return ae;
        }
        List<AiopsExamItemMap> aiopsExamItemMapList = aiopsExamMapper.selectAiopsExamItemIndexList(Lists.newArrayList(ae.getData()));
        return ResponseBase.ok(aiopsExamItemMapList.stream().map(AiopsExamItemMap::getAiopsItem).collect(Collectors.toList()));
    }

    @Override
    public BaseResponse saveNetworkSecurityData(NetworkSecurityDataParams networkSecurityDataParams) {
        // 参数校验
        BaseResponse baseResponse = checkParams(networkSecurityDataParams);
        if (!baseResponse.checkIsSuccess()) {
            return baseResponse;
        }

        String modelPk = networkSecurityDataParams.getModelPk();
        SaveParams srSaveParams = networkSecurityDataParams.getSrSaveParams();
        String id = networkSecurityDataParams.getId();
        String modelCode = networkSecurityDataParams.getModelCode();

        if (StringUtils.isEmpty(id)) {
            generateAndSetId(networkSecurityDataParams, modelPk, srSaveParams);
        } else {
            updateExistingId(networkSecurityDataParams, modelPk, srSaveParams, modelCode);
        }


        // 保存数据
//        apiService.saveData(srSaveParams);
        BaseResponse responseBase = apiService.saveDataStream(srSaveParams);
        if (!responseBase.checkIsSuccess()) {
            return responseBase;
        }

        // 保存详情
        restUtil.saveMrDetail(
                networkSecurityDataParams.getEid(),
                networkSecurityDataParams.getModelCode(),
                networkSecurityDataParams.getId(),
                networkSecurityDataParams.getTargetValue()
        );

        return BaseResponse.ok();
    }

//    private void srSaveData(){
//        StarRocksEntity starRocksEntity = new StarRocksEntity();
//        starRocksEntity.setDatabase(bigDataUtil.getSrDbName());
//        starRocksEntity.setTable("prediction_model_log");
//        List<LinkedHashMap<String, Object>> rows = new ArrayList<>();
//        LinkedHashMap<String, Object> params = new LinkedHashMap<>();
//        params.put("reqId", reqId);
//        params.put("generateDate", DateUtil.getNowFormatString(DATE_FORMATTER));
//        params.put("targetModelCode", targetModelCode);
//        params.put("sourceModelCode", sourceModelCode);
//        params.put("aiFileUrl", fileInfo.getOrDefault("fileUrl", ""));
//        params.put("requestModel", gson.toJson(requestModel));
//        params.put("responseModel", gson.toJson(responseModel));
//        params.put("selectSql", fileInfo.getOrDefault("sql", ""));
//        params.put("collectedTime", DateUtil.getNowFormatString(DATE_TIME_FORMATTER));
//        params.put("fileName", fileInfo.getOrDefault("fileName", ""));
//        rows.add(params);
//        starRocksEntity.setRows(rows);
//        bigDataUtil.srStreamLoad(starRocksEntity);
//    }

    private void generateAndSetId(NetworkSecurityDataParams params, String modelPk, SaveParams srSaveParams) {
        String newId;
        if ("eid".equals(modelPk)) {
            Long eid = params.getEid();
            newId = LongUtil.safeToString(eid);
            JSONObject jsonObject = srSaveParams.getData().getJSONObject(BASIC_INFO);
            jsonObject.put(modelPk, eid);
        } else {
            long id = SnowFlake.getInstance().newId();
            newId = LongUtil.safeToString(id);
            JSONArray dataContent = srSaveParams.getData().getJSONArray(DATA_CONTENT);
            // 此处只会有一条记录
            dataContent.getJSONObject(0).put(modelPk, id);
        }
        params.setId(newId);
    }

    private void updateExistingId(NetworkSecurityDataParams params, String modelPk, SaveParams srSaveParams, String modelCode) {
        // 获取数据内容和基本信息
        JSONObject data = srSaveParams.getData();
        JSONArray dataContent = data.getJSONArray(DATA_CONTENT);
        JSONObject basicInfo = data.getJSONObject(BASIC_INFO);
        
        // 更新ID值
        dataContent.getJSONObject(0).put(modelPk, params.getId());
        basicInfo.put(modelPk, params.getId());
        
        // 获取aiopsItemId，优先从dataContent中获取，如果为空则从basicInfo中获取
        String dataContentAiopsItemId = StringUtil.toString(dataContent.getJSONObject(0).get(AIOPS_ITEMID));
        String basicInfoAiopsItemId = StringUtil.toString(basicInfo.get(AIOPS_ITEMID));
        String aiopsItemId = StringUtils.isEmpty(dataContentAiopsItemId) ? basicInfoAiopsItemId : dataContentAiopsItemId;

        // 构建查询SQL获取资产ID
        String querySql = String.format("SELECT %s FROM %s%s WHERE %s=%s",
                AIOPS_ITEMID, MODEL_DB, modelCode, modelPk, params.getId());
        
        // 执行查询
        List<Map<String, Object>> existAssetList = bigDataUtil.srQuery(querySql);
        
        // 检查是否需要删除旧数据并生成新ID
        if (!CollectionUtils.isEmpty(existAssetList)) {
            String existingAiopsItemId = StringUtil.toString(existAssetList.get(0).get(AIOPS_ITEMID));
            if (!Objects.equals(existingAiopsItemId, aiopsItemId)) {
                // 如果aiopsItemId不匹配，删除旧数据并生成新ID
                params.setModelPkValue(params.getId());
                deleteNetworkSecurityDataById(params);
                generateAndSetId(params, modelPk, srSaveParams);
            }
        }
    }

    private BaseResponse checkParams(NetworkSecurityDataParams networkSecurityDataParams) {
        Optional<BaseResponse> optResponse = checkParamIsEmpty(networkSecurityDataParams.getModelPk(), "modelPk");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        optResponse = checkParamIsEmpty(networkSecurityDataParams.getEid(), "eid");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        optResponse = checkParamIsEmpty(networkSecurityDataParams.getModelCode(), "modelCode");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        optResponse = checkParamIsEmpty(networkSecurityDataParams.getSrSaveParams(), "srSaveParams");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        optResponse = checkParamIsEmpty(networkSecurityDataParams.getSrSaveParams().getDbName(), "dbName");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        optResponse = checkParamIsEmpty(networkSecurityDataParams.getSrSaveParams().getSchemaName(), "schemaName");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        optResponse = checkParamIsEmpty(networkSecurityDataParams.getSrSaveParams().getSinkType(), "sinkType");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        optResponse = checkParamIsEmpty(networkSecurityDataParams.getSrSaveParams().getTableName(), "tableName");
        return optResponse.orElseGet(BaseResponse::ok);
    }

    @Override
    public BaseResponse deleteNetworkSecurityDataById(NetworkSecurityDataParams networkSecurityDataParams) {
        Optional<BaseResponse> optResponse = checkParamIsEmpty(networkSecurityDataParams.getModelPk(), "modelPk");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        optResponse = checkParamIsEmpty(networkSecurityDataParams.getModelPkValue(), "modelPkValue");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        optResponse = checkParamIsEmpty(networkSecurityDataParams.getModelCode(), "modelCode");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        optResponse = checkParamIsEmpty(networkSecurityDataParams.getEid(), "eid");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        // 从请求参数中提取必要数据
        String modelPk = networkSecurityDataParams.getModelPk();
        String modelPkValue = networkSecurityDataParams.getModelPkValue();
        String modelCode = networkSecurityDataParams.getModelCode();
        Long eid = networkSecurityDataParams.getEid();

        String deleteSql = String.format("UPDATE %s%s SET isDeleted = 1 WHERE %s=%s",
                MODEL_DB, modelCode, modelPk, modelPkValue);
        bigDataUtil.srSave(deleteSql);

        // 资产未被使用，可以删除
        restUtil.removeMrDetailByIdList(eid, modelCode, Collections.singletonList(modelPkValue));
        // 检查资产是否存在
//        if (!CollectionUtils.isEmpty(existAssetList)) {
//            String aiopsItemId = StringUtil.toString(existAssetList.get(0).get("aiopsItemId"));
        // 检查资产是否被使用
//            if (aiopsExamMapper.selectAeInstanceByAiopsItemId(aiopsItemId) == 0) {


//            } else {
        // 资产已被使用，返回错误
//                return BaseResponse.error(AIOPS_EXAM_INSTANCE_USED);
//            }
//        }

        return BaseResponse.ok();
    }

    @Override
    public ResponseBase<Long> getNetworkSecurityAe(AiopsExamRecord record) {
        Long examId;
        if ("Simple".equals(record.getReportType())) {
            examId = networkSecurityExamMapper.selectExamIdByCode("Network_Security_Examination_Simple");
            if (examId == null) {
                return ResponseBase.error(AIOPS_EXAM_ERROR);
            }
            return ResponseBase.okT(examId);
        } else {
            examId = networkSecurityExamMapper.selectExamIdByCode("Network_Security_Examination");
            if (examId == null) {
                return ResponseBase.error(AIOPS_EXAM_ERROR);
            }
        }
        return ResponseBase.okT(examId);
    }

    @Override
    public ResponseBase getNetworkSecurity(Long eid, Long aerId) {
        // infoSystem總數
        StringBuilder totalSql = new StringBuilder(" SELECT count(*) total FROM " +
                "servicecloud.NetworkSecurityExamInfoSystem WHERE isDeleted = 0 AND eid=" + eid);
        List<Map<String, Object>> ListsInfoSystemCount = bigDataUtil.srQuery(totalSql.toString());
        int total = 0;
        if (!ListsInfoSystemCount.isEmpty()) {
            Object totalObj = ListsInfoSystemCount.get(0).get("total");
            if (totalObj instanceof Number) {
                total = ((Number) totalObj).intValue();
            }
        }

        StringBuilder totalDataSql = new StringBuilder(" SELECT securityLevel, count(securityLevel) AS `count` " +
                "FROM servicecloud.NetworkSecurityExamInfoSystem WHERE isDeleted = 0 AND eid=" + eid
                + " GROUP by securityLevel");
        List<Map<String, Object>> ListInfoSystemCount = bigDataUtil.srQuery(totalDataSql.toString());

        // infoSystem等級數量、詳細
        List<AiopsExamItemInstanceScore> aiopsExamItemInstanceScores =
                networkSecurityExamMapper.selectExamItemInstanceScore(aerId);
        int levelCount = 0;

        List<Long> idList;
        List<Map<String, Object>> levelCountData = null;
        if (!aiopsExamItemInstanceScores.isEmpty()) {
            levelCount = aiopsExamItemInstanceScores.size();
            idList = aiopsExamItemInstanceScores.stream()
                    .map(AiopsExamItemInstanceScore::getNetworkExamAssetId)
                    .collect(Collectors.toList());

            String idListSql = idList.stream()
                    .map(String::valueOf)
                    .collect(Collectors.joining(",", "(", ")"));

            StringBuilder sbInfoSystemDataSql = new StringBuilder(" SELECT securityLevel, count(securityLevel) AS `count`" +
                    " FROM servicecloud.NetworkSecurityExamInfoSystem WHERE isDeleted = 0 AND eid = " + eid +
                    " AND infoSystemId IN " + idListSql + " GROUP by securityLevel");
            levelCountData = bigDataUtil.srQuery(sbInfoSystemDataSql.toString());
        }

        // orgInfo詳細
        StringBuilder sbOrgInfosql = new StringBuilder(" SELECT * FROM servicecloud.NetworkSecurityAuditOrgInfo WHERE eid=" + eid);
        List<Map<String, Object>> ListOrgInfo = bigDataUtil.srQuery(sbOrgInfosql.toString());

        Map<String, Object> result = new LinkedHashMap<>();
        result.put("total", total);
        result.put("totalData", ListInfoSystemCount);
        result.put("levelCount", levelCount);
        result.put("levelCountData", levelCountData);
        result.put("selfCount", levelCount);
        result.put("selfCountData", levelCountData);
        result.put("orgInfoList", ListOrgInfo);

        return ResponseBase.okT(result);
    }

    @Override
    public BaseResponse saveNetworkSecurityExamSimpleReport(AiopsExamRecordsReportRecord reportRecord, int scale) {

        // 1. 插入报告记录
        BaseResponse<AiopsExamRecordsReportRecord> baseResponse = aiopsExamService.insertAiopsExamRecordsReportRecord(reportRecord);
        if (!baseResponse.checkIsSuccess()) {
            log.error("[saveNetworkSecurityExamSimpleReport] insert report error: {}", baseResponse);
            return baseResponse;
        }
        AiopsExamRecordsReportRecord savedRecord = baseResponse.getData();
        
        CompletableFuture.runAsync(() -> {
            
            // 2. 获取分数和indexTypeList
            BaseResponse<AiopsExamRecordsReportRecord> reportScoreBr = getReportScore(savedRecord.getAerId(), scale);
            if (!reportScoreBr.checkIsSuccess()) {
                log.error("[saveNetworkSecurityExamSimpleReport] getReportScore error: {}", reportScoreBr);
                return;
            }
            AiopsExamRecordsReportRecord scoreData = reportScoreBr.getData();

            // 3. 结构化数据
            List<Map<String, Object>> summaryList = new ArrayList<>();
            if (scoreData.getIndexTypeList() != null) {
                Map<String, String> additionalContentMap = getAeiAdditionalContentMap();
                for (AiopsExamIndexType indexType : scoreData.getIndexTypeList()) {
                    Map<String, Object> indexTypeMap = getStringObjectMap(indexType, additionalContentMap);
                    summaryList.add(indexTypeMap);
                }
            }
            String summaryJson = JSONObject.toJSONString(summaryList);

            // 4. 汇总结论
            NetworkSecurityReportRequest request = new NetworkSecurityReportRequest();
            request.setQuestion(summaryJson);
            ResponseBase<String> conclusionResp = aioChatFeignClient.getReportSummary(request);
            if (conclusionResp != null && conclusionResp.checkIsSuccess()) {
                savedRecord.setExamReportContent(conclusionResp.getData());
            } else {
                savedRecord.setExamReportContent("");
            }

            savedRecord.setIndexTypeList(scoreData.getIndexTypeList());
            // 5. 保存报告到ES
            networkSecurityExamSimpleEsReport.generateReport(savedRecord);

            // 6. 更新报告状态
            aiopsExamRecordMapper.updateReportStatus(savedRecord.getId(), ReportStatus.UNDER_EVA.getIndex(), LocalDateTime.now());
        });
        return BaseResponse.ok(savedRecord.getId());
    }

    private static Map<String, Object> getStringObjectMap(AiopsExamIndexType indexType, Map<String, String> additionalContentMap) {
        Map<String, Object> indexTypeMap = new HashMap<>();
        indexTypeMap.put("indexTypeName", indexType.getName());
        List<Map<String, Object>> aeiList = new ArrayList<>();
        if (indexType.getAeiList() != null) {
            for (AiopsExamIndex aei : indexType.getAeiList()) {
                Map<String, Object> aeiMap = new HashMap<>();
                aeiMap.put("name", aei.getName());
                aeiMap.put("referenceValue", aei.getReferenceValue());
                if (aei.getAeis() != null) {
                    boolean isNormal = "HEALTH".equals(aei.getAeis().getLevelCode());
                    aeiMap.put("isNormal", isNormal);
                    String key = aei.getCode() + "_" + isNormal;
                    String desc = additionalContentMap.get(key);
                    aeiMap.put("additionalDesc", desc);
                    aei.setAdditionalDesc(desc);
                    aei.setIsNormal(isNormal);
                }
                aeiList.add(aeiMap);
            }
        }
        indexTypeMap.put("aeiList", aeiList);
        return indexTypeMap;
    }

    private Map<String, String> getAeiAdditionalContentMap() {
        List<AiopsExamIndexReportAdditionalContent> list = aiopsExamIndexReportAdditionalContentMapper.selectAll();
        Map<String, String> result = new HashMap<>();
        for (AiopsExamIndexReportAdditionalContent item : list) {
            String key = item.getAeiCode() + "_" + item.getConditionsCode();
            String value = item.getConditionsDesc();
            result.put(key, value);
        }
        return result;
    }

    @Override
    public BaseResponse getNetworkSecurityExamSimpleReport(String id) {
        try {
            return BaseResponse.ok(networkSecurityExamSimpleEsReport.getReport(id));
        } catch (Exception e) {
            log.error("[getNetworkSecurityExamSimpleReport] error, id={}, {}", id, e.getMessage(), e);
            return BaseResponse.error(e);
        }
    }

    @Override
    public BaseResponse updateNetworkSecurityExamSimpleReport(String id, String fieldPath, Object value) {
        try {
            networkSecurityExamSimpleEsReport.updateReport(id, fieldPath, value);
            return BaseResponse.ok();
        } catch (Exception e) {
            log.error("[updateNetworkSecurityExamSimpleReport] error, id={}, fieldPath={}, value={}, {}", id, fieldPath, value, e.getMessage(), e);
            return BaseResponse.error(e);
        }
    }

    @Override
    public BaseResponse getExamItemInstanceScoreTotal(Long aerId) {
        List<AiopsExamItemInstanceScore> aiopsExamItemInstanceScores =
                networkSecurityExamMapper.selectExamItemInstanceScoreTotal(aerId);
        List<Map<String, Object>> result = new ArrayList<>();
        if (!CollectionUtils.isEmpty(aiopsExamItemInstanceScores)) {
            for (AiopsExamItemInstanceScore score : aiopsExamItemInstanceScores) {
                Map<String, Object> map = new HashMap<>();
                map.put("networkExamCategoryCode", score.getNetworkExamCategoryCode());
                map.put("aiopsItem", score.getAiopsItem());
                map.put("aiopsItemTotal", score.getAiopsItemTotal());
                result.add(map);
            }
        }
        return BaseResponse.ok(result);
    }

    /**
     * 批量获取examId
     */
    public ResponseBase<List<Long>> getNetworkSecurityAeBatch(List<String> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return ResponseBase.error(PARAM_IS_EMPTY.getCode(), "codes不能为空");
        }
        List<AiopsExam> pairs = networkSecurityExamMapper.selectExamIdByCodes(codes);
        if (pairs == null || pairs.isEmpty()) {
            return ResponseBase.error(AIOPS_EXAM_ERROR);
        }
        List<Long> collect = pairs.stream().map(AiopsExam::getId).collect(Collectors.toList());
        return ResponseBase.okT(collect);
    }
}
