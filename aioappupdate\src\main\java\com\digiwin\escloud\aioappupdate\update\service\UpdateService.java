package com.digiwin.escloud.aioappupdate.update.service;

import com.alibaba.fastjson.JSON;
import com.digiwin.escloud.aioappupdate.release.dao.ReleaseMapper;
import com.digiwin.escloud.aioappupdate.release.model.Release;
import com.digiwin.escloud.aioappupdate.update.dao.UpdateMapper;
import com.digiwin.escloud.aioappupdate.update.model.*;
import com.digiwin.escloud.aioitms.model.device.AiopsKitDevice;
import com.digiwin.escloud.aioitms.model.device.AiopsKitDeviceTypeMapping;
import com.digiwin.escloud.aiomail.Mail;
import com.digiwin.escloud.aiomail.MailSourceType;
import com.digiwin.escloud.common.controller.ParamCheckHelp;
import com.digiwin.escloud.common.feign.AioBasicFeignClient;
import com.digiwin.escloud.common.feign.AioItmsFeignClient;
import com.digiwin.escloud.common.feign.AioMailFeignClient;
import com.digiwin.escloud.common.feign.AioUserFeignClient;
import com.digiwin.escloud.common.model.ResponseBase;
import com.digiwin.escloud.common.model.ResponseCode;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.common.response.PageInfo;
import com.digiwin.escloud.common.util.*;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@Slf4j
@Service
public class UpdateService implements IUpdateService, ParamCheckHelp {

    @Autowired
    private UpdateMapper updateMapper;
    @Autowired
    private ReleaseMapper releaseMapper;
    @Autowired
    private AioBasicFeignClient aioBasicFeignClient;
    @Autowired
    private AioUserFeignClient aioUserFeignClient;
    @Autowired
    private AioItmsFeignClient aioItmsFeignClient;
    @Autowired
    private AioMailFeignClient aioMailFeignClient;

    @Value("${digiwin.supplier.defaultsid:241199971893824}")
    private Long defaultSid;
    @Value("${aio.service.area}")
    private String serviceArea;
    @Value("${digiwin.default.warning.level:FATAL}")
    private String[] defaultWarningLevels;

    @Override
    public BaseResponse getUpdateLogList(int pageIndex, int size, String productCode, String baseVersion, String deviceName, String tenantName, String releaseName, String releaseVersion, String status, String startDate, String endDate, String updateChannel) {
        Map<String, Object> map = new HashMap<>();
        map.put("sid", RequestUtil.getHeaderSid());
        map.put("productCode", productCode);
        map.put("baseVersion", baseVersion);
        map.put("deviceName", deviceName);
        map.put("tenantName", tenantName);
        map.put("releaseName", releaseName);
        map.put("releaseVersion", releaseVersion);
        map.put("status", status);
        map.put("startDate", startDate);
        map.put("endDate", endDate);
        map.put("updateChannel", updateChannel);

        Page page = PageHelper.startPage(pageIndex, size);
        updateMapper.getUpdateLogList(map);
        PageInfo<Update> pageInfo = new PageInfo<Update>(page);
        return BaseResponse.ok(pageInfo);
    }

    private long getReleaseId(String productCode, String baseVersion, String platformVersion, String releaseVersion, String releaseNo) {
        Map<String, Object> map = new HashMap<>();
        map.put("sid", RequestUtil.getHeaderSid());
        map.put("productCode", productCode);
        map.put("baseVersion", baseVersion);
        map.put("releaseVersionToo", releaseVersion);
        map.put("platformVersion", platformVersion);
        map.put("releaseNo", releaseNo);
        List<Release> list = releaseMapper.getReleaseList(map);
        if (!CollectionUtils.isEmpty(list)) {
            return list.get(0).getId();
        }
        return 0;
    }

    @Override
    public BaseResponse sendRequest(String productCode, String baseVersion, String platformVersion,
                                    String releaseVersion, String releaseNo, String serviceCode,
                                    String collectCode, String warningCode, Update update) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(update, "update");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion
        /*if(RequestUtil.getHeaderEid() == 0L && StringUtils.isEmpty(serviceCode)){
            return BaseResponse.error(ResponseCode.PARAM_VERIFY);
        }*/
        //默认取header里面的eid
        update.setEid(RequestUtil.getHeaderEid());
        try {
            //T工具没有传eid，可以更加serviceCode得到eid
            if (RequestUtil.getHeaderEid() == 0L && !StringUtils.isEmpty(serviceCode)) {
                //1先根据客代 查到eid
                ResponseBase res = aioBasicFeignClient.syncTenant(serviceCode);
                if (ResponseCode.SUCCESS.toString().equals(res.getCode())) {
                    Optional.ofNullable(res.getData()).ifPresent(o -> {
                        Long eid = JSON.parseObject(JSON.toJSONString(o), Long.class);
                        update.setEid(eid);
                    });
                }
            }

        } catch (Exception e) {
            log.error("aioBasicFeignClient.syncTenant", e);
        }

        //2 更加版本号查版更id
        long arId = getReleaseId(productCode, baseVersion, platformVersion, releaseVersion, releaseNo);
        //更新单头
        update.setId(SnowFlake.getInstance().newId());
        update.setArId(arId);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        update.setUpdateReqNo(sdf.format(new Date()));
        List<UpdateProcess> processList = update.getProcessList();
        if (!CollectionUtils.isEmpty(processList)) {
            update.setStartTime((processList.stream()
                    .min(Comparator.comparing(UpdateProcess::getOperationTime)).get()).getOperationTime());
            UpdateProcess maxUpdateProcess = processList.stream()
                    .max(Comparator.comparing(UpdateProcess::getOperationTime)).get();
            update.setEndTime(maxUpdateProcess.getOperationTime());
            update.setStatus(maxUpdateProcess.getOperation());
        }
        update.setProductCode(productCode);
        update.setBaseVersion(baseVersion);
        update.setReleaseVersion(releaseVersion);
        update.setSid(RequestUtil.getHeaderSidOrDefault(defaultSid));
        updateMapper.saveUpdate(update);

        //更新单身
        if (!CollectionUtils.isEmpty(processList)) {
            Map<String, List<UpdateProcess>> operationGroupMap = processList.stream().map(k -> {
                k.setId(SnowFlake.getInstance().newId());
                k.setAuId(update.getId());
                k.setSid(update.getSid());
                if (StringUtils.isEmpty(k.getOperation())) {
                    k.setOperation("");
                }
                updateMapper.insertUpdateProgress(k);
                return k;
            }).collect(Collectors.groupingBy(UpdateProcess::getOperation));

            //3检查并发送预警
            checkAndSendWarning(update.getId(), collectCode, warningCode, operationGroupMap);
        }

        return BaseResponse.ok(update.getUpdateReqNo());
    }

    @Override
    public BaseResponse saveUpdateLog(String productCode, String baseVersion, String platformVersion,
                                      String releaseVersion, String serviceCode, String updateReqNo,
                                      String collectCode, String warningCode, Update update) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(updateReqNo, "updateReqNo");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        optResponse = checkParamIsEmpty(update, "update");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        //根据请求单号查到au_update.id
        Long updateId = updateMapper.getUpdateId(updateReqNo);
        if (LongUtil.isEmpty(updateId)) {
            return BaseResponse.error(ResponseCode.UPDATE_REQ_NO_NOT_EXIST);
        }
        List<UpdateProcess> processList = update.getProcessList();
        if (CollectionUtils.isEmpty(processList)) {
            return BaseResponse.ok(0);
        }
        //1更新单头
        UpdateProcess maxUpdateProcess = processList.stream()
                .max(Comparator.comparing(UpdateProcess::getOperationTime)).get();
        update.setEndTime(maxUpdateProcess.getOperationTime());
        update.setStatus(maxUpdateProcess.getOperation());
        update.setUpdateReqNo(updateReqNo);
        updateMapper.modifyUpdate(update);

        //2更新单身
        Map<String, List<UpdateProcess>> operationGroupMap = processList.stream().map(k -> {
            k.setId(SnowFlake.getInstance().newId());
            k.setAuId(updateId);
            if (LongUtil.isEmpty(k.getSid())) {
                k.setSid(RequestUtil.getHeaderSidOrDefault(defaultSid));
            }
            if (StringUtils.isEmpty(k.getOperation())) {
                k.setOperation("");
            }
            updateMapper.insertUpdateProgress(k);
            return k;
        }).collect(Collectors.groupingBy(UpdateProcess::getOperation));

        //3检查并发送预警
        checkAndSendWarning(updateId, collectCode, warningCode, operationGroupMap);

        return BaseResponse.ok(updateReqNo);
    }

    private void checkAndSendWarning(Long updateId, String collectCode, String warningCode,
                                     Map<String, List<UpdateProcess>> operationGroupMap) {
        if (LongUtil.isEmpty(updateId) || StringUtils.isBlank(collectCode) || StringUtils.isBlank(warningCode) ||
                CollectionUtils.isEmpty(operationGroupMap)) {
            return;
        }
        Map<String, Object> map = new HashMap<>(3);
        map.put("resultType", "FAIL");
        map.put("serviceArea", serviceArea);
        map.put("codeList", operationGroupMap.keySet());
        List<UpdateOperation> operationList = updateMapper.selectUpdateOperationByMap(map);
        if (CollectionUtils.isEmpty(operationList)) {
            return;
        }
        map.clear();
        map.put("id", updateId);
        List<Update> updateList = updateMapper.getUpdateLogList(map);
        if (CollectionUtils.isEmpty(updateList)) {
            return;
        }
        Update update = updateList.stream().filter(Objects::nonNull).findFirst().orElse(null);
        if (update == null) {
            return;
        }
        Supplier<StringBuilder> sbSupplier = () -> {
            StringBuilder sbLog = new StringBuilder();
            sbLog.append("checkAndSendWarning updateId:");
            sbLog.append(updateId);
            return sbLog;
        };
        Long eid = update.getEid();
        if (LongUtil.isEmpty(eid)) {
            log.error(sbSupplier.get().append(" ignore by eid is empty").toString());
            return;
        }
        String deviceId = update.getDeviceId();
        if (StringUtils.isBlank(deviceId)) {
            log.error(sbSupplier.get().append(" ignore by deviceId is empty").toString());
            return;
        }
        String[] warningLevels = this.defaultWarningLevels;
        if (ArrayUtils.isEmpty(warningLevels)) {
            log.error(sbSupplier.get().append(" ignore by defaultWarningLevels is empty").toString());
            return;
        }
        BaseResponse<AiopsKitDevice> response = aioItmsFeignClient.getDeviceBasicInfo(deviceId);
        if (!response.checkIsSuccess()) {
            StringBuilder sbError = sbSupplier.get().append(" getDeviceBasicInfo result code:");
            sbError.append(response.getCode());
            sbError.append(" not zero, errMsg:");
            sbError.append(response.getErrMsg());
            log.error(sbError.toString());
            return;
        }
        AiopsKitDevice device = response.getData();
        if (device == null) {
            StringBuilder sbError = sbSupplier.get().append(" deviceId:");
            sbError.append(deviceId);
            sbError.append(" result aiopsKitDevice is empty");
            log.error(sbError.toString());
            return;
        }
        List<AiopsKitDeviceTypeMapping> deviceTypeMappingList = device.getDeviceTypeMappingList();
        if (CollectionUtils.isEmpty(deviceTypeMappingList)) {
            StringBuilder sbError = sbSupplier.get().append(" deviceId:");
            sbError.append(deviceId);
            sbError.append(" result deviceTypeMappingList is empty");
            log.error(sbError.toString());
            return;
        }
        String deviceType = deviceTypeMappingList.stream()
                .filter(Objects::nonNull).map(AiopsKitDeviceTypeMapping::getDeviceType)
                .filter(StringUtils::isNotBlank).findFirst().orElse("");
        if (StringUtils.isEmpty(deviceType)) {
            StringBuilder sbError = sbSupplier.get().append(" deviceId:");
            sbError.append(deviceId);
            sbError.append(" result deviceType is empty");
            log.error(sbError.toString());
            return;
        }
        Arrays.stream(warningLevels).forEach(x -> {
            ResponseBase<List<Map<String, Object>>> responseBase = aioUserFeignClient.getNoticeGroupMail(collectCode,
                    warningCode, x, deviceId, deviceType, LongUtil.safeToString(eid));
            Supplier<StringBuilder> sbSubLog = () ->
                    sbSupplier.get().append(" deviceId:").append(deviceId)
                            .append(" collectCode:").append(collectCode)
                            .append(" warningCode:").append(warningCode)
                            .append(" deviceType:").append(deviceType)
                            .append(" eid:").append(eid);

            if (!responseBase.checkIsSuccess()) {
                StringBuilder sbError = sbSubLog.get();
                sbError.append(" getNoticeGroupMail result code:");
                sbError.append(response.getCode());
                sbError.append(" not zero, errMsg:");
                sbError.append(response.getErrMsg());
                log.error(sbError.toString());
                return;
            }
            List<Map<String, Object>> mapList = responseBase.getData();
            if (CollectionUtils.isEmpty(mapList)) {
                StringBuilder sbError = sbSubLog.get();
                sbError.append(" getNoticeGroupMail result data is empty");
                log.info(sbError.toString());
                return;
            }
            //因为只可能命中一个群组，因此可以全部混在一起发送
            List<String> ccList = new ArrayList<>();
            List<String> toList = mapList.stream().filter(y -> !CollectionUtils.isEmpty(y)).map(y -> {
                //cc
                String mailCC = Objects.toString(y.get("mailCC"), "");
                if (!StringUtils.isBlank(mailCC)) {
                    List<String> cc = Arrays.stream(mailCC.split(";")).filter(StringUtils::isNotBlank)
                            .collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(cc)) {
                        ccList.addAll(cc);
                    }
                }
                //to
                Optional<List<Map<String, Object>>> optGsuiList = MapUtil.getListMap(y, "groupStaffUserInfos");
                List<Map<String, Object>> gsuiList;
                if (!optGsuiList.isPresent() || CollectionUtils.isEmpty(gsuiList = optGsuiList.get())) {
                    return null;
                }
                return gsuiList.stream().map(z ->
                        //TODO:日后要实现
                        //z.get("telephone")
                        //z.get("wechat")
                        Objects.toString(z.get("email"), "")
                ).filter(StringUtils::isNotBlank).collect(Collectors.toList());
            }).filter(Objects::nonNull).flatMap(Collection::stream).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(toList)) {
                StringBuilder sbError = sbSubLog.get();
                sbError.append(" getNoticeGroupMail result receivers is empty");
                log.info(sbError.toString());
                return;
            }
            Mail mail = new Mail();
            mail.setCcs(ccList);
            mail.setReceivers(toList);
            mail.setMailSourceType(MailSourceType.AutoUpdateWarning);
            //因为没有语言别的配置，不进行设置暂时直接吃aiomail的语言别(如:zh_CN)
            //mail.setLanguage(serviceArea);
            Map<String, Object> processParams = new HashMap<>();
            processParams.put("deviceName", device.getDeviceName());
            processParams.put("deviceType", deviceType);
            processParams.put("warningLevel", x);
            processParams.put("tenantName", update.getTenantName());
            String nowString = DateUtil.getNowFormatString(DateUtil.DATE_TIME_FORMATTER);
            processParams.put("warningTime", nowString);
            processParams.put("colletedTime", nowString);
            processParams.put("warningCount", 1);
            processParams.put("ipAddress", device.getIpAddress());
            processParams.put("contentKey", "auto_update_fail");
            processParams.put("suggestKey", "auto_update_suggestion");
            processParams.put("detailTitlePrefixKey", "auto_update_detail_title_");
            //单身
            processParams.put("detailFields", new String[]{"productCode", "updateTime", "releaseNo", "releaseVersion",
                    "operation", "operationContent", "remark"});
            processParams.put("detailSourceKey", "detailSource");
            processParams.put("detailSource", operationList.stream()
                    .filter(y -> operationGroupMap.containsKey(y.getCode()))
                    .map(y -> {
                        List<UpdateProcess> upList = operationGroupMap.get(y.getCode());
                        return upList.stream().map(z -> {
                            Map<String, Object> currentMap = new HashMap<>();
                            currentMap.put("productCode", update.getProductCategory());
                            currentMap.put("updateTime", z.getOperationTime());
                            currentMap.put("releaseNo", update.getReleaseNo());
                            currentMap.put("releaseVersion", update.getReleaseVersion());
                            currentMap.put("operation", y.getCurrentName());
                            currentMap.put("operationContent", z.getOperationContent());
                            currentMap.put("remark", z.getRemark());
                            return currentMap;
                        }).collect(Collectors.toList());
                    }).flatMap(Collection::stream).collect(Collectors.toList()));
            mail.setProcessParams(processParams);
            aioMailFeignClient.sendMail(mail);
        });
    }

    @Override
    public BaseResponse getStatus(String updateReqNo) {
        Map<String, Object> map = new HashMap<>();
        map.put("updateReqNo", updateReqNo);
        map.put("sid", RequestUtil.getHeaderSid());
        map.put("eid", RequestUtil.getHeaderEid());

        return BaseResponse.ok(updateMapper.getStatus(map));
    }

    @Override
    public BaseResponse<List<UpdateProcess>> getUpdateProgress(Long auId) {
        Map<String, Object> map = new HashMap<>();
        map.put("auId", auId);
        return BaseResponse.ok(updateMapper.getUpdateProgress(map));
    }

    @Override
    public BaseResponse stopUpdate(Long id, String status, String operationContent) {
        Map<String, Object> map1 = new HashMap<>();
        map1.put("id", id);
        List<Update> list = updateMapper.getUpdateLogList(map1);
        if (!CollectionUtils.isEmpty(list)) {
            if (UpdateStatus.INSTALL_SUCCESS.toString().equals(list.get(0).getStatus())) {
                return BaseResponse.error(ResponseCode.CURRENT_STATUS_CAN_NOT_HAND_STOP);
            }
        }

        UpdateProcess process = new UpdateProcess();
        process.setId(SnowFlake.getInstance().newId());
        process.setSid(RequestUtil.getHeaderSid());
        process.setAuId(id);
        process.setOperation(UpdateStatus.HAND_STOP.toString());
        process.setOperator(UpdateChannel.AIOPS.toString());
        process.setOperationContent(operationContent);
        DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        process.setOperationTime(sdf.format(new Date()));
        updateMapper.insertUpdateProgress(process);

        Map<String, Object> map = new HashMap<>();
        map.put("id", id);
        map.put("status", status);
        map.put("endTime", process.getOperationTime());
        updateMapper.stopUpdate(map);

        return BaseResponse.ok();
    }

    @Override
    public BaseResponse getOperateLogList(int pageIndex, int size, String tenantName, String deviceName, String productCode, String operation, String status, String releaseVersion, String releaseName, String startDate, String endDate) {
        Map<String, Object> map = new HashMap<>();
        map.put("sid", RequestUtil.getHeaderSid());
        map.put("tenantName", tenantName);
        map.put("deviceName", deviceName);
        map.put("productCode", productCode);
        map.put("operation", operation);
        map.put("status", status);
        map.put("releaseVersion", releaseVersion);
        map.put("releaseName", releaseName);
        map.put("startDate", startDate);
        map.put("endDate", endDate);

        Page page = PageHelper.startPage(pageIndex, size);
        updateMapper.getOperateLogList(map);
        PageInfo<Update> pageInfo = new PageInfo<Update>(page);
        return BaseResponse.ok(pageInfo);
    }


    @Override
    public BaseResponse saveOperateLog(String serviceCode, OperateLog operateLog) {
        /*if(RequestUtil.getHeaderEid() == 0L && StringUtils.isEmpty(serviceCode)){
            return BaseResponse.error(ResponseCode.PARAM_VERIFY);
        }*/
        //默认取header里面的eid
        operateLog.setEid(RequestUtil.getHeaderEid());
        try {
            //T工具没有传eid，可以更加serviceCode得到eid
            if (RequestUtil.getHeaderEid() == 0L && !StringUtils.isEmpty(serviceCode)) {
                ResponseBase res = aioBasicFeignClient.syncTenant(serviceCode);
                if (ResponseCode.SUCCESS.toString().equals(res.getCode())) {
                    Optional.ofNullable(res.getData()).ifPresent(o -> {
                        Long eid = JSON.parseObject(JSON.toJSONString(o), Long.class);
                        operateLog.setEid(eid);
                    });
                }
            }

        } catch (Exception e) {
            log.error("aioBasicFeignClient.syncTenant", e);
        }

        //2 保存操作记录
        operateLog.setId(SnowFlake.getInstance().newId());
        operateLog.setSid(RequestUtil.getHeaderSid());
        return BaseResponse.ok(updateMapper.saveOperateLog(operateLog));
    }

    @Override
    public BaseResponse getUpdateOperationList(String resultType) {
        Map<String, Object> map = new HashMap<>(2);
        map.put("resultType", resultType);
        map.put("serviceArea", serviceArea);
        return BaseResponse.ok(updateMapper.selectUpdateOperationByMap(map));
    }
}
