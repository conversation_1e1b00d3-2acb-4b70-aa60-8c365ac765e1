package com.digiwin.escloud.aioappupdate.release.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

/**
 * 禅道对接
 */
@Service
@Slf4j
public class CloudIssueService implements ICloudIssueService {

    @Value("${cloudissueservice.address}")
    private String cloudAddress;

    @Override
    public void delete(String fileId, String bucketName){
        try{
            RestTemplate restTemplate= new RestTemplate();
            restTemplate.delete(cloudAddress + "api/file/delete?bucketName="+bucketName + "&fileId=" + fileId);
        }catch (Exception ex){
            ex.printStackTrace();
        }
    }

}
