package com.digiwin.escloud.aiobasic.assetrisk.dao;

import com.digiwin.escloud.aiobasic.assetrisk.model.AssetSystem;
import com.digiwin.escloud.aiobasic.assetrisk.model.AssetSystemMapping;
import com.digiwin.escloud.aiobasic.assetrisk.model.dto.*;
import com.digiwin.escloud.aiobasic.assetrisk.model.param.AssetSystemParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

public interface AssetSystemMapper {

    int insertRiskAssetSystem(AssetSystem system);
    int updateRiskAssetSystem(AssetSystem system);
    int deleteRiskAssetSystemById(String id);
    int deleteRiskAssetSystemMappingBySystemId(String assetSystemId);
    List<AssetDTO> selectRiskAssetBySystem(@Param("assessmentId") String assessmentId,@Param("assetSystemName") String assetSystemName);
    List<AssetDTO> selectRiskAssetNoSystem(AssetSystemParam param);
    List<AssetSystemMappingDTO> selectSystemByAssetId(@Param("assetId")String assetId);
    List<AssetSystemMapping> selectMappingByAssetSystemId(@Param("assetSystemId") String assetSystemId);

    List<AssetSystem> selectRiskAssetSystemByAssessmentId(@Param("assessmentId")String assessmentId,@Param("assetId")String assetId);

    List<AssetSystemDTO> selectRiskAssetSystemDTOByAssessmentId(@Param("assessmentId")String assessmentId, @Param("assetId")String assetId);
    List<AssetSystemDTO> selectRiskAssetSystemByCondition(@Param("assessmentId") String assessmentId,
                                                       @Param("assetSystemName")String assetSystemName);
    List<AssetSystemDTO> selectRiskAssetSystemAndAssetByAssessmentId(@Param("assessmentId") String assessmentId);
    List<ProcessDTO> selectSystemCntByAssessmentId(@Param("set") Set<String> assessmentIds);
    List<MoveSystemDTO> selectMoveCntByAssessmentId(@Param("set") Set<String> assessmentIds);
    AssetSystem selectRiskAssetSystemById(String id);
    List<AssetSystem> selectRiskAssetSystemByIds(String[] ids);

    int insertRiskAssetSystemMapping(List<AssetSystemMapping> list);
    int deleteRiskAssetSystemMappingByAssetId(@Param("assetSystemId") String assetSystemId,@Param("assetId") String assetId);
    List<AssetSystem> selectMaxValueByAssetSystemIds(@Param("ids") List<AssetSystemDTO> ids);
}
