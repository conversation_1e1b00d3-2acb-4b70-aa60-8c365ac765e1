package com.digiwin.escloud.aiobasic.assetrisk.config;

import com.digiwin.escloud.aiobasic.assetrisk.model.CommonEnumVO;
import com.digiwin.escloud.aiobasic.assetrisk.model.enums.CommonEnum;
import com.digiwin.escloud.aiobasic.assetrisk.service.CommonEnumRegistry;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;
import java.util.List;
import java.util.Map;


@Configuration
public class CommonEnumJacksonCustomizer {
    @Autowired
    private CommonEnumRegistry enumRegistry;

    @Bean
    public Jackson2ObjectMapperBuilderCustomizer commonEnumBuilderCustomizer(){
        return builder ->{
            Map<Class, List<CommonEnum>> classDict = enumRegistry.getClassDict();
            classDict.forEach((aClass, commonEnums) -> {
                builder.serializerByType(aClass, new CommonEnumJsonSerializer());
            });

        };
    }

    static class CommonEnumJsonSerializer extends JsonSerializer{

        @Override
        public void serialize(Object o, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
            CommonEnum commonEnum = (CommonEnum) o;
            Integer from = CommonEnumVO.from(commonEnum);
            jsonGenerator.writeObject(from);
        }
    }


}
