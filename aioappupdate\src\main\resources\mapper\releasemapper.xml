<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.digiwin.escloud.aioappupdate.release.dao.ReleaseMapper">
    <resultMap id="ReleaseMap" type="com.digiwin.escloud.aioappupdate.release.model.Release">
        <id column="id" property="id"/>
        <result column="sid" property="sid"/>
        <result column="productCode" property="productCode"/>
        <result column="productCategory" property="productCategory"/>
        <result column="abvId" property="abvId"/>
        <result column="platformVersion" property="platformVersion"/>
        <result column="releaseVersion" property="releaseVersion"/>
        <result column="sortVersion" property="sortVersion"/>
        <result column="releaseNo" property="releaseNo"/>
        <result column="releaseName" property="releaseName"/>
        <result column="description" property="description"/>
        <result column="releaseExplain" property="releaseExplain"/>
        <result column="arfId" property="arfId"/>
        <result column="fileType" property="fileType"/>
        <result column="autoUpdateScript" property="autoUpdateScript"/>
        <result column="releaser" property="releaser"/>
        <result column="releaseTime" property="releaseTime"/>
        <result column="status" property="status"/>
        <result column="isOpenWhite" property="isOpenWhite"/>
        <collection property="releaseFile" columnPrefix="rf_" resultMap="ReleaseFileMap"/>
        <collection property="baseVersion" columnPrefix="bv_" resultMap="BaseVersionMap"/>
    </resultMap>
    <resultMap id="ReleaseFileMap" type="com.digiwin.escloud.aioappupdate.release.model.ReleaseFile">
        <id column="id" property="id"/>
        <result column="sid" property="sid"/>
        <result column="fileId" property="fileId"/>
        <result column="fileName" property="fileName"/>
        <result column="fileSize" property="fileSize"/>
        <result column="url" property="url"/>
        <result column="uploadTime" property="uploadTime"/>
    </resultMap>
    <resultMap id="BaseVersionMap" type="com.digiwin.escloud.aioappupdate.release.model.BaseVersion">
        <id column="id" property="id"/>
        <result column="sid" property="sid"/>
        <result column="productCode" property="productCode"/>
        <result column="baseVersion" property="baseVersion"/>
        <result column="isAutoUpdate" property="isAutoUpdate"/>
        <result column="authorizationMethod" property="authorizationMethod"/>
        <result column="remark" property="remark"/>
    </resultMap>
    <select id="getBaseVersionList" resultType="com.digiwin.escloud.aioappupdate.release.model.BaseVersion">
        SELECT *
        FROM au_base_version a
        WHERE 1=1
        <if test="sid != null and sid != ''">
            AND a.sid = #{sid}
        </if>
        <if test="productCode !=null and productCode!=''">
            AND a.productCode = #{productCode}
        </if>
        order by a.baseVersion asc
    </select>

    <select id="getBaseVersion" resultType="com.digiwin.escloud.aioappupdate.release.model.BaseVersion">
        SELECT *
        FROM au_base_version a
        WHERE 1=1
        <if test="id != null and id != ''">
            AND a.id = #{id}
        </if>
        <if test="sid != null and sid != ''">
            AND a.sid = #{sid}
        </if>
        <if test="productCode !=null and productCode!=''">
            AND a.productCode = #{productCode}
        </if>
        <if test="baseVersion !=null and baseVersion!=''">
            AND a.baseVersion = #{baseVersion}
        </if>
        limit 1
    </select>

    <select id="findBaseVersion" parameterType="com.digiwin.escloud.aioappupdate.release.model.BaseVersion" resultType="java.lang.Integer">
        select count(*) from au_base_version a
        WHERE 1=1
        <if test="sid != null and sid != ''">
            AND a.sid = #{sid}
        </if>
        <if test="productCode !=null and productCode!=''">
            AND a.productCode = #{productCode}
        </if>
        <if test="baseVersion !=null and baseVersion!=''">
            AND a.baseVersion = #{baseVersion}
        </if>
    </select>
    <insert id="addBaseVersion" parameterType="com.digiwin.escloud.aioappupdate.release.model.BaseVersion">
        insert into au_base_version(id, sid, productCode, baseVersion, isAutoUpdate, authorizationMethod, remark)
        values(#{id}, #{sid}, #{productCode}, #{baseVersion}, #{isAutoUpdate}, #{authorizationMethod}, #{remark})
    </insert>
    <update id="updateBaseVersion" parameterType="com.digiwin.escloud.aioappupdate.release.model.BaseVersion">
        update au_base_version
        set sid = #{sid}, productCode = #{productCode}, baseVersion = #{baseVersion}, isAutoUpdate = #{isAutoUpdate}, authorizationMethod = #{authorizationMethod}, remark = #{remark}
        where id =#{id}
    </update>
    <delete id="deleteBaseVersion">
        delete from au_base_version where id = #{abvId}
    </delete>

    <select id="getReleaseList" resultMap="ReleaseMap">
        SELECT ar.*,abv.productCode,sp.productCategory,
        arf.id rf_id, arf.sid rf_sid,  arf.fileId rf_fileId,  arf.fileName rf_fileName,arf.fileSize rf_fileSize, arf.url rf_url, arf.uploadTime rf_uploadTime,
        abv.id bv_id,abv.sid bv_sid,abv.productCode bv_productCode,abv.baseVersion bv_baseVersion,abv.isAutoUpdate bv_isAutoUpdate,abv.authorizationMethod bv_authorizationMethod,abv.remark bv_remark
        FROM au_release ar
        left join au_base_version abv on ar.abvId = abv.id
        left join supplier_product sp on sp.sid = ar.sid and sp.productCode = abv.productCode
        left join au_release_file arf on arf.id = ar.arfId
        WHERE 1=1
        <if test="sid != null and sid != ''">
            AND ar.sid = #{sid}
        </if>
        <if test="productCode !=null and productCode!=''">
            AND abv.productCode = #{productCode}
        </if>
        <if test="baseVersion !=null and baseVersion!=''">
            AND abv.baseVersion = #{baseVersion}
        </if>
        <if test="abvId !=null and abvId!=''">
            AND ar.abvId = #{abvId}
        </if>
        <if test="status !=null and status!=''">
            AND ar.status = #{status}
        </if>
        <if test="releaseVersion !=null and releaseVersion!=''">
            AND ar.releaseVersion like "%"#{releaseVersion}"%"
        </if>
        <if test="releaseNo !=null and releaseNo!=''">
            AND ar.releaseNo = #{releaseNo}
        </if>
        <if test="releaseVersionToo !=null and releaseVersionToo!=''">
            AND ar.releaseVersion = #{releaseVersionToo}
        </if>
        <if test="releaseName !=null and releaseName!=''">
            AND (ar.releaseNo like "%"#{releaseName}"%" or ar.releaseName like "%"#{releaseName}"%")
        </if>
        <if test="platformVersion !=null and platformVersion!=''">
            AND ar.platformVersion like "%"#{platformVersion}"%"
        </if>
        <if test="startDate !=null and startDate!='' and endDate !=null and endDate!=''">
            AND DATE_FORMAT(ar.releaseTime, '%Y-%m-%d') between #{startDate} and #{endDate}
        </if>
        <if test="openWhite !=null and openWhite!=''">
            AND ar.isOpenWhite = #{openWhite}
        </if>
        order by ar.releaseTime desc,ar.sortVersion desc
    </select>

    <select id="getReleaseTreeList" resultType="com.digiwin.escloud.aioappupdate.release.model.Release">
        SELECT ar.*
        FROM au_release ar
        left join au_base_version abv on ar.abvId = abv.id
        WHERE exists(select 1 from au_release_white_tenant arwh where arwh.arId = ar.id)
        <if test="sid != null and sid != ''">
            AND ar.sid = #{sid}
        </if>
        <if test="productCode !=null and productCode!=''">
            AND abv.productCode = #{productCode}
        </if>
        <if test="baseVersion !=null and baseVersion!=''">
            AND abv.baseVersion = #{baseVersion}
        </if>
        <if test="abvId !=null and abvId!=''">
            AND ar.abvId = #{abvId}
        </if>
        <if test="status !=null and status!=''">
            AND ar.status = #{status}
        </if>
        <if test="releaseVersion !=null and releaseVersion!=''">
            AND ar.releaseVersion like "%"#{releaseVersion}"%"
        </if>
        <if test="releaseNo !=null and releaseNo!=''">
            AND ar.releaseNo = #{releaseNo}
        </if>
        <if test="releaseVersionToo !=null and releaseVersionToo!=''">
            AND ar.releaseVersion = #{releaseVersionToo}
        </if>
        <if test="releaseName !=null and releaseName!=''">
            AND (ar.releaseNo like "%"#{releaseName}"%" or ar.releaseName like "%"#{releaseName}"%")
        </if>
        <if test="platformVersion !=null and platformVersion!=''">
            AND ar.platformVersion like "%"#{platformVersion}"%"
        </if>
        <if test="startDate !=null and startDate!='' and endDate !=null and endDate!=''">
            AND DATE_FORMAT(ar.releaseTime, '%Y-%m-%d') between #{startDate} and #{endDate}
        </if>
        <if test="openWhite !=null and openWhite!=''">
            AND ar.isOpenWhite = #{openWhite}
        </if>
        order by ar.releaseTime desc,ar.sortVersion desc
    </select>

    <select id="getReleaseNos" resultType="java.lang.String">
        SELECT distinct ar.releaseNo
        FROM au_release ar
        left join au_base_version abv on ar.abvId = abv.id
        left join supplier_product sp on sp.sid = ar.sid and sp.productCode = abv.productCode
        WHERE ar.status = 1
        <if test="sid !=null and sid!=''">
            AND ar.sid = #{sid}
        </if>
        <if test="productCode !=null and productCode!=''">
            AND abv.productCode = #{productCode}
        </if>
        <if test="baseVersion !=null and baseVersion!=''">
            AND abv.baseVersion = #{baseVersion}
        </if>
        <if test="platformVersion !=null and platformVersion!=''">
            AND ar.platformVersion = #{platformVersion}
        </if>

    </select>

    <select id="getNewVersion" resultType="com.digiwin.escloud.aioappupdate.release.model.Release">
        SELECT ar.*,abv.productCode,sp.productCategory
        FROM au_release ar
        left join au_base_version abv on ar.abvId = abv.id
        left join supplier_product sp on sp.sid = ar.sid and sp.productCode = abv.productCode
        left join au_release_white_tenant arwt ON arwt.arId = ar.id
        WHERE ar.status = 1
        <if test="sid !=null and sid!=''">
            AND ar.sid = #{sid}
        </if>
        <if test="productCode !=null and productCode!=''">
            AND abv.productCode = #{productCode}
        </if>
        <if test="baseVersion !=null and baseVersion!=''">
            AND abv.baseVersion = #{baseVersion}
        </if>
        <if test="platformVersion !=null and platformVersion!=''">
            AND ar.platformVersion = #{platformVersion}
        </if>
        <if test="releaseNo !=null and releaseNo!=''">
            AND ar.releaseNo = #{releaseNo}
        </if>
        <if test="serviceCode !=null and serviceCode!=''">
            AND (ar.isOpenWhite = 0 or (ar.isOpenWhite = 1 AND arwt.serviceCode = #{serviceCode}))
        </if>
        <if test="eid !=null and eid!='' and eid!=0">
            AND (ar.isOpenWhite = 0 or (ar.isOpenWhite = 1 AND arwt.eid = #{eid}))
        </if>
        order by ar.sortVersion desc
        limit 1
    </select>
    <select id="getReleaseDetail" resultType="com.digiwin.escloud.aioappupdate.release.model.Release">
        SELECT ar.*,abv.productCode,sp.productCategory
        FROM au_release ar
        left join au_base_version abv on ar.abvId = abv.id
        left join supplier_product sp on sp.sid = ar.sid and sp.productCode = abv.productCode
        WHERE 1=1
        <if test="sid !=null and sid!=''">
            AND ar.sid = #{sid}
        </if>
        <if test="arId !=null and arId!=''">
            AND ar.id = #{arId}
        </if>
        <if test="arfId !=null and arfId!=''">
            AND ar.arfId = #{arfId}
        </if>
        limit 1
    </select>
    <insert id="release" parameterType="com.digiwin.escloud.aioappupdate.release.model.Release" >
        insert into au_release(id, sid, abvId, platformVersion, releaseVersion, sortVersion, releaseNo, releaseName, description, releaseExplain,
        arfId, fileType, autoUpdateScript, releaser, releaseTime, status, isOpenWhite)
        values(#{id}, #{sid}, #{abvId}, #{platformVersion}, #{releaseVersion}, #{sortVersion}, #{releaseNo}, #{releaseName}, #{description}, #{releaseExplain},
        #{arfId}, #{fileType}, #{autoUpdateScript}, #{releaser}, #{releaseTime}, #{status}, #{isOpenWhite})
        ON DUPLICATE KEY UPDATE sid = #{sid}, abvId = #{abvId}, platformVersion = #{platformVersion}, releaseVersion = #{releaseVersion},
        sortVersion = #{sortVersion}, releaseNo = #{releaseNo}, releaseName = #{releaseName}, description = #{description}, releaseExplain = #{releaseExplain},
        arfId = #{arfId}, fileType = #{fileType}, autoUpdateScript = #{autoUpdateScript}, releaser = #{releaser},
        releaseTime = #{releaseTime}, status = #{status},isOpenWhite = #{isOpenWhite}
    </insert>

    <select id="checkReleaseVersion" resultType="java.lang.Integer" >
        SELECT count(*)
        FROM au_release ar
        WHERE 1=1
        <if test="sid != null and sid != ''">
            AND ar.sid = #{sid}
        </if>
        <if test="releaseNo !=null and releaseNo!=''">
            AND ar.releaseNo = #{releaseNo}
        </if>
        <if test="releaseVersion !=null and releaseVersion!=''">
            AND ar.releaseVersion = #{releaseVersion}
        </if>
        <if test="abvId !=null and abvId!=''">
            AND ar.abvId = #{abvId}
        </if>
        <if test="id !=null and id!=''">
            AND ar.id != #{id}
        </if>
    </select>
    <select id="getReleaseFile" resultType="com.digiwin.escloud.aioappupdate.release.model.ReleaseFile" >
        select * from au_release_file a where a.id = #{id}
    </select>

    <delete id="deleteReleaseFile" >
        delete from au_release_file where id = #{id}
    </delete>
    <delete id="updateReleaseFileId" >
        update au_release a set a.arfId = 0 where id = #{arId}
    </delete>

    <insert id="insertReleaseFile" parameterType="com.digiwin.escloud.aioappupdate.release.model.ReleaseFile" >
        insert into au_release_file(id, sid, fileId, fileName, fileSize, url, uploadTime)
        values(#{id}, #{sid}, #{fileId}, #{fileName}, #{fileSize}, #{url}, #{uploadTime})
        ON DUPLICATE KEY UPDATE fileId = #{fileId}, fileName = #{fileName}, fileSize = #{fileSize}, url = #{url}, uploadTime = #{uploadTime}
    </insert>

    <select id="getWhiteTenants" resultType="com.digiwin.escloud.aioappupdate.release.model.ReleaseWhiteTenant" >
        SELECT arwt.*,t.NAME tenantName
        FROM au_release_white_tenant arwt
        LEFT JOIN tenant t ON arwt.eid = t.sid
        where 1=1
        <if test="sid !=null and sid!=''">
            AND arwt.sid = #{sid}
        </if>
        <if test="arId !=null and arId!=''">
            AND arwt.arId = #{arId}
        </if>
        <if test="eid !=null and eid!=''">
            AND arwt.eid = #{eid}
        </if>
        <if test="serviceCode !=null and serviceCode!=''">
            AND arwt.serviceCode = #{serviceCode}
        </if>
        order by arwt.addTime desc
    </select>
    <insert id="insertWhiteTenants" parameterType="com.digiwin.escloud.aioappupdate.release.model.ReleaseWhiteTenant" >
        insert into au_release_white_tenant(id, sid, eid, serviceCode, arId, addTime)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id},#{item.sid},#{item.eid},#{item.serviceCode},#{item.arId},#{item.addTime})
        </foreach>
    </insert>

    <select id="getExistWhiteTenants" resultType="com.digiwin.escloud.aioappupdate.release.model.ReleaseWhiteTenant" >
        select * from au_release_white_tenant arwt where 1=1
        <if test="sid !=null and sid!=''">
            AND arwt.sid = #{sid}
        </if>
        <if test="arId !=null and arId!=''">
            AND arwt.arId = #{arId}
        </if>
    </select>
    <delete id="deleteWhiteTenants">
        delete from au_release_white_tenant where arId = #{arId} and id = #{arwtId}
    </delete>

    <delete id="deleteAllWhiteTenants">
        delete from au_release_white_tenant where arId = #{arId}
    </delete>

    <update id="updateStatus">
        update au_release a
        set a.status = #{status}, a.releaseTime = #{releaseTime}
        where a.id = #{arId}
    </update>

    <delete id="deleteRelease">
        delete from au_release where id = #{arId}
    </delete>

    <update id="setWhiteStatus">
        update au_release a set a.isOpenWhite = #{openWhite} where id = #{arId}
    </update>

    <select id="getReleaseFiles" resultType="com.digiwin.escloud.aioappupdate.release.model.ReleaseFileDetail">
        SELECT arf.*,
        abv.productCode,sp.productCategory,ar.id arId,ar.releaseVersion,ar.releaseNo,ar.releaseName
        FROM au_release_file arf
        LEFT JOIN au_release ar ON arf.id = ar.arfId
        LEFT JOIN au_base_version abv ON abv.id = ar.abvId
        LEFT JOIN supplier_product sp ON sp.productCode = abv.productCode AND sp.sid = ar.sid
        WHERE ar.fileType ='FILE'
        <if test="sid != null and sid != ''">
            AND arf.sid = #{sid}
        </if>
        <if test="productCode !=null and productCode!=''">
            AND abv.productCode = #{productCode}
        </if>
        <if test="baseVersion !=null and baseVersion!=''">
            AND abv.baseVersion = #{baseVersion}
        </if>
        ORDER BY arf.uploadTime DESC
    </select>

</mapper>