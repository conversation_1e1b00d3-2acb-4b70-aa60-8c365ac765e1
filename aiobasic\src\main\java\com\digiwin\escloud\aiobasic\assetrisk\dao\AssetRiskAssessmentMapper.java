package com.digiwin.escloud.aiobasic.assetrisk.dao;

import com.digiwin.escloud.aiobasic.assetrisk.model.AssetRiskAssessment;
import com.digiwin.escloud.aiobasic.assetrisk.model.dto.AssetRiskAssessmentDTO;
import com.digiwin.escloud.aiobasic.assetrisk.model.param.RiskAssessmentParam;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectKey;

import java.util.List;
import java.util.Set;

public interface AssetRiskAssessmentMapper {

    void insertRiskAssessment(AssetRiskAssessment riseAssessment);
    int updateRiskAssessment(RiskAssessmentParam riseAssessment);
    int updateRiskAssessmentStatus(RiskAssessmentParam riseAssessment);
    int deleteRiseAssessment(RiskAssessmentParam  param);
    List<AssetRiskAssessmentDTO> findRiskAssessment(RiskAssessmentParam riseAssessment);

    /**
     * 根据评估id删除相关操作
     * @param assessmentId
     * @return
     */
    @Select("SELECT id FROM risk_asset WHERE assessmentId =#{assessmentId}")
    List<String> selectAssetByAssessmentId(@Param("assessmentId") String assessmentId);

    @Select("SELECT * FROM risk_assessments WHERE id =#{assessmentId}")
    AssetRiskAssessment selectByAssessmentId(@Param("assessmentId") String assessmentId);

    @Delete("delete from risk_asset where assessmentId =#{assessmentId}")
    void deleteAssetByAssessmentId(@Param("assessmentId") String assessmentId);

    void deleteAssetMappingSystemByAssetIds(@Param("ids") List<String> ids);

    void deleteAssetStandardMappingByAssetIds(@Param("ids") List<String> ids);

    @Select("SELECT id FROM risk_asset_system WHERE assessmentId =#{assessmentId}")
    List<String> selectSystemByAssessmentId(@Param("assessmentId") String assessmentId);
    @Delete("delete from risk_asset_system where assessmentId =#{assessmentId}")
    void deleteSystemByAssessmentId(@Param("assessmentId") String assessmentId);

    void deleteAssetMappingSystemBySystemIds(@Param("ids") List<String> ids);

    void deleteRiskAnalyseBySystemIds(@Param("ids") List<String> ids);

    void deleteAnalyseMappingPlanByAnalyseIds(@Param("ids") Set<String> analyseIds);
    @Delete("delete from risk_possibilities where assessmentId =#{assessmentId}")
    void deleteRiskPossibilitiesByAssessmentId(String assessmentId);
    @Delete("delete from risk_vulnerability where assessmentId =#{assessmentId}")
    void deleteRiskVulnerabilityByAssessmentId(String assessmentId);
    @Delete("delete from risk_level where assessmentId =#{assessmentId}")
    void deleteRiskLevelByAssessmentId(String assessmentId);
    @Delete("delete from risk_curr_control_plan where assessmentId =#{assessmentId}")
    void deleteCurrPlanByAssessmentId(String assessmentId);
    @Delete("delete from risk_improve_plan where assessmentId =#{assessmentId}")
    void deleteImprovePlanByAssessmentId(String assessmentId);
    @Delete("delete from risk_asset_value_standard where assessmentId =#{assessmentId}")
    void deleteValueStandardByAssessmentId(String assessmentId);
}
