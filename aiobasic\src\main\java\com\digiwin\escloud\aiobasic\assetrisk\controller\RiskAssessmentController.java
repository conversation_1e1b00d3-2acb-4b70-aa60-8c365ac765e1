package com.digiwin.escloud.aiobasic.assetrisk.controller;

import com.alibaba.excel.annotation.ExcelProperty;
import com.digiwin.escloud.aiobasic.assetrisk.model.AssetExcelData;
import com.digiwin.escloud.aiobasic.assetrisk.model.AssetRiskAssessment;
import com.digiwin.escloud.aiobasic.assetrisk.model.param.RiskAssessmentParam;
import com.digiwin.escloud.aiobasic.assetrisk.model.param.UploadAssetParam;
import com.digiwin.escloud.aiobasic.assetrisk.service.AssetRiskAssessmentService;
import com.digiwin.escloud.common.controller.ControllerBase;
import com.digiwin.escloud.common.model.ResponseBase;
import com.digiwin.escloud.common.response.BaseResponse;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

import static com.digiwin.escloud.common.model.ResponseCode.SUCCESS;

@Api(value = "/risk/assessment", protocols = "HTTP", tags = {"风险评估首页相关接口"}, description = "风险评估首页相关接口")
@RestController
@RequestMapping("/risk/assessment")
public class RiskAssessmentController extends ControllerBase {
    @Resource
    private AssetRiskAssessmentService assessmentService;

    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "保存风险评估")
    @PostMapping("/assessmentSave")
    public ResponseBase assessmentSave(@RequestBody AssetRiskAssessment assessment){
       return assessmentService.riskAssessmentSave(assessment);
    }


    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "生成资产清单excel")
    @GetMapping("/excelTemplate")
    public BaseResponse excelTemplate(String style){
        return this.getBaseResponse((x)->{
            List<AssetExcelData> templateData = new ArrayList<>();
            AssetExcelData data = new AssetExcelData();
            data.setAssetNo("範例");
            data.setAssessmentType("硬體");
            data.setAssetType("XX");
            data.setAssetName("FortiGate 100D");
            data.setAssetSystem("FIREWALL");
            data.setAssetNum(1);
            data.setAssetPos("資訊機房");
            data.setAssetCompany("資訊室");
            data.setAssetOwner("XXX");
            data.setAssetOwnerCompany("無");
            data.setAssetMaintainStatus("無合約");
            templateData.add(data);
            String url = assessmentService.createAssessmentRiskExcel(templateData,
                    StringUtils.isNotBlank(style) && style.equals("2") ? "软硬件资产清单模板_v1" : "軟硬件資產清單模板_v1","模板");
            x.setData(url);
            return x;
        },true,false,SUCCESS);
    }
    @ApiResponses({
            @ApiResponse(code = 1, message = "资产清单格式填写有误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "上传资产清单接口")
    @PostMapping("/updateAsset")
    public ResponseBase updateAsset(@RequestBody UploadAssetParam param){
        return assessmentService.parseAssessmentRiskExcel(param);
    }
    @ApiResponses({
            @ApiResponse(code = 1, message = "状态修改失败"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "风险评估将已取消的风险评估修改为评估中")
    @PostMapping("/assessmentStart")
    public ResponseBase assessmentStart(@RequestBody RiskAssessmentParam assessment){
        return   assessmentService.riskAssessmentStart(assessment);
    }
    @ApiResponses({
            @ApiResponse(code = 1, message = "状态修改失败"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "风险评估将评估中的风险评估修改为已经取消")
    @PostMapping("/assessmentCancel")
    public ResponseBase assessmentCancel(@RequestBody RiskAssessmentParam assessment){
       return assessmentService.riskAssessmentCancel(assessment);
    }
    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "风险评估分页查询")
    @PostMapping("/assessmentGet")
    public BaseResponse assessmentGet(@RequestBody RiskAssessmentParam assessment){
        return this.getBaseResponse((x)->{
            PageInfo assetRiskAssessments = assessmentService.riskAssessmentFind(assessment);
            x.setData(assetRiskAssessments);
            return x;
        },true,false,SUCCESS);
    }

    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "判断资产清单是否已经上传")
    @PostMapping("/assessmentUrlGet")
    public ResponseBase assessmentUrlGet(@RequestBody RiskAssessmentParam assessment){
        return assessmentService.assetUrlGet(assessment);
    }
    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "风险评估更新内容，只可以更新eid、title、评估人id、评估人名称")
    @PutMapping("/assessmentUpdate")
    public ResponseBase assessmentUpdate(@RequestBody RiskAssessmentParam assessment){
        return assessmentService.riskAssessmentUpdate(assessment);
    }

    @ApiResponses({
            @ApiResponse(code = 1, message = "状态修改失败"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "风险评估将已经取消的风险评估删除，现在的删除不完全，暂时不会把相关资产全部清空")
    @DeleteMapping("/assessmentDelete")
    public ResponseBase assessmentDelete(@RequestBody RiskAssessmentParam assessment){
        return  assessmentService.riskAssessmentDelete(assessment);
    }
}
