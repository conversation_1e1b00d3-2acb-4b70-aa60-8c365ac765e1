package com.digiwin.escloud.aiobasic.assetrisk.model.enums;

public enum PlanStatus implements CommonEnum{


    NO_PLAN(0,"未計劃"),
    ALREADY_PLAN(1,"已計劃"),
    IN_REVIEW(2,"審核中"),
    REVIEW_COMPLETE(3,"已審批"),
    EXECUTING(4,"執行中"),
    EXECUTING_COMPLETE(5,"執行完成");
    ;
    private final int code;
    private final String desc;

    PlanStatus(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getDescription() {
        return desc;
    }
}
