package com.digiwin.escloud.aiobasic.assetrisk.model.dto;

import com.digiwin.escloud.aiobasic.assetrisk.model.RiskAssessmentType;
import com.digiwin.escloud.aiobasic.assetrisk.model.base.AssetRiseAssessmentBase;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Set;

@EqualsAndHashCode(callSuper = true)
@Data
public class AssetSystemDTO extends AssetRiseAssessmentBase {

    private String assessmentId;
    private String assetSystemName;
    private String assetNames;
    private Set<RiskAssessmentType> riskAssessmentTypeList;
    private Integer value;
    private Integer valueC;
    private Integer valueI;
    private Integer valueA;
    private Integer valueCom;
    private String remark;
    private Integer assetCount;
    private int riskAnalyseIncompleteCnt;
    private int riskAnalyseImproveIncompleteCnt;
    private int riskAnalyseImproveCompleteCnt;
    private int riskAnalyseCompleteCnt;





}
