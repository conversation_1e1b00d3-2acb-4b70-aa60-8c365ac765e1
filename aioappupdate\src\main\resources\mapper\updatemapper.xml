<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.digiwin.escloud.aioappupdate.update.dao.UpdateMapper">
    <resultMap id="updateOperation" type="com.digiwin.escloud.aioappupdate.update.model.UpdateOperation">
        <id column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="name" property="name"/>
        <result column="name_CN" property="name_CN"/>
        <result column="name_TW" property="name_TW"/>
        <result column="resultType" property="resultType"/>
        <result column="currentName" property="currentName"/>
    </resultMap>

    <select id="getUpdateLogList" resultType="com.digiwin.escloud.aioappupdate.update.model.Update">
        SELECT au.*,
               t.NAME tenantName,
               abv.productCode, abv.baseVersion,
               sp.productCategory,
               ar.releaseVersion, ar.releaseNo, ar.releaseName
        FROM au_update au
        LEFT JOIN au_release ar ON ar.id = au.arId
        LEFT JOIN au_base_version abv ON abv.id = ar.abvId
        left join supplier_product sp on sp.sid = au.sid and sp.productCode = abv.productCode
        LEFT JOIN supplier_tenant_map stm ON stm.sid = au.sid AND stm.eid= au.eid
        LEFT JOIN tenant t ON t.sid = stm.eid
        WHERE 1=1
        <if test="sid != null and sid != ''">
            AND au.sid = #{sid}
        </if>
        <if test="productCode !=null and productCode!=''">
            AND abv.productCode = #{productCode}
        </if>
        <if test="baseVersion !=null and baseVersion!=''">
            AND abv.baseVersion = #{baseVersion}
        </if>
        <if test="deviceName !=null and deviceName!=''">
            AND au.deviceName like "%"#{deviceName}"%"
        </if>
        <if test="tenantName !=null and tenantName!=''">
            AND t.NAME like "%"#{tenantName}"%"
        </if>
        <if test="status !=null and status!=''">
            AND au.status = #{status}
        </if>
        <if test="releaseName !=null and releaseName!=''">
            AND (ar.releaseName like "%"#{releaseName}"%" or ar.releaseNo like "%"#{releaseName}"%")
        </if>
        <if test="releaseVersion !=null and releaseVersion!=''">
            AND ar.releaseVersion like "%"#{releaseVersion}"%"
        </if>
        <if test="updateChannel !=null and updateChannel!=''">
            AND au.updateChannel like "%"#{updateChannel}"%"
        </if>
        <if test="startDate !=null and startDate!='' and endDate !=null and endDate!=''">
            AND DATE_FORMAT(au.startTime, '%Y-%m-%d') between #{startDate} and #{endDate}
        </if>
        <if test="id !=null and id!='' ">
            AND au.id = #{id}
        </if>
        order by au.startTime desc
    </select>

    <select id="getUpdateProgress" resultType="com.digiwin.escloud.aioappupdate.update.model.UpdateProcess">
        SELECT * FROM au_update_progress aup
        WHERE 1=1
        <if test="auId !=null and auId!=''">
            AND aup.auId = #{auId}
        </if>
        ORDER BY aup.operationTime desc
    </select>

    <update id="stopUpdate">
        update au_update au
        set au.status = #{status}, au.endTime = #{endTime} where au.id = #{id}
    </update>


    <insert id="saveUpdate" parameterType="com.digiwin.escloud.aioappupdate.update.model.Update" >
        insert into au_update(id, sid, eid, arId, updateReqNo, deviceId, deviceName, updateMode, updateChannel, status, startTime, endTime)
        values(#{id}, #{sid}, #{eid}, #{arId}, #{updateReqNo}, #{deviceId}, #{deviceName}, #{updateMode}, #{updateChannel}, #{status}, #{startTime}, #{endTime} )
    </insert>
    <update id="modifyUpdate" parameterType="com.digiwin.escloud.aioappupdate.update.model.Update">
        update au_update
        set endTime = #{endTime}, status = #{status},
        <if test="deviceId != null and deviceId != ''">
            deviceId = #{deviceId},
        </if>
        <if test="deviceName != null and deviceName != ''">
            deviceName = #{deviceName},
        </if>
        updateMode = #{updateMode}, updateChannel = #{updateChannel}
        where updateReqNo =#{updateReqNo}
    </update>

    <select id="getUpdateId" resultType="java.lang.Long">
        select id from au_update where updateReqNo = #{updateReqNo}
    </select>

    <insert id="insertUpdateProgress" parameterType="com.digiwin.escloud.aioappupdate.update.model.UpdateProcess" >
        insert into au_update_progress(id, sid, auId, operator, operationTime, operation, operationContent, remark)
        values(#{id}, #{sid}, #{auId}, #{operator}, #{operationTime}, #{operation}, #{operationContent}, #{remark})
    </insert>
    <select id="getOperateLogList" resultType="com.digiwin.escloud.aioappupdate.update.model.OperateLog">
        SELECT aol.*,t.NAME tenantName,sp.productCategory
        FROM au_operate_log aol

        LEFT JOIN au_base_version abv ON abv.productCode = aol.productCode AND abv.baseVersion = aol.baseVersion
        left join supplier_product sp on sp.sid = aol.sid and sp.productCode = aol.productCode
        LEFT JOIN supplier_tenant_map stm ON stm.sid = aol.sid AND stm.eid= aol.eid
        LEFT JOIN tenant t ON t.sid = stm.eid
        WHERE 1=1
        <if test="sid != null and sid != ''">
            AND aol.sid = #{sid}
        </if>
        <if test="tenantName !=null and tenantName!=''">
            AND t.NAME like "%"#{tenantName}"%"
        </if>
        <if test="deviceName!=null and deviceName!=''">
            AND aol.deviceName like "%"#{deviceName}"%"
        </if>
        <if test="productCode !=null and productCode!=''">
            AND aol.productCode = #{productCode}
        </if>
        <if test="operation !=null and operation!=''">
            AND aol.operation like "%"#{operation}"%"
        </if>
        <if test="status !=null and status!=''">
            AND aol.operatorStatus like "%"#{status}"%"
        </if>
        <if test="releaseVersion !=null and releaseVersion!=''">
            AND aol.releaseVersion like "%"#{releaseVersion}"%"
        </if>
        <if test="releaseName !=null and releaseName!=''">
            AND (aol.releaseName like "%"#{releaseName}"%" or aol.releaseNo like "%"#{releaseName}"%")
        </if>
        <if test="startDate !=null and startDate!='' and endDate !=null and endDate!=''">
            AND DATE_FORMAT(aol.operationTime, '%Y-%m-%d %H:%i:%s') between #{startDate} and #{endDate}
        </if>
        order by aol.operationTime desc
    </select>


    <insert id="saveOperateLog" parameterType="com.digiwin.escloud.aioappupdate.update.model.OperateLog" >
        insert into au_operate_log(id, sid, eid, deviceId, deviceName, productCode, baseVersion, releaseVersion, releaseNo,
        releaseName, operator, operation, operationTime, operatorFrom, operatorStatus, remark)
        values(#{id},#{sid}, #{eid}, #{deviceId}, #{deviceName}, #{productCode}, #{baseVersion}, #{releaseVersion}, #{releaseNo},
        #{releaseName}, #{operator}, #{operation}, #{operationTime}, #{operatorFrom}, #{operatorStatus}, #{remark} )
    </insert>

    <select id="getStatus" resultType="java.lang.String">
        select au.status from au_update au
        where 1=1
        <if test="sid != null and sid != ''">
            AND au.sid = #{sid}
        </if>
        <if test="eid != null and eid != ''">
            AND au.eid = #{eid}
        </if>
        <if test="updateReqNo != null and updateReqNo != ''">
            AND au.updateReqNo = #{updateReqNo}
        </if>
        limit 1
    </select>

    <select id="selectUpdateOperationByMap" resultMap="updateOperation">
        SELECT id, code, `name`, name_CN, name_TW, resultType,
        <choose>
            <when test="serviceArea == 'TW'">
                name_TW AS currentName
            </when>
            <when test="serviceArea == 'CN'">
                name_CN AS currentName
            </when>
            <otherwise>
                `name` AS currentName
            </otherwise>
        </choose>
        FROM au_update_operation
        WHERE 1 = 1
        <if test="resultType != null and resultType != ''">
            AND resultType = #{resultType}
        </if>
        <if test="codeList != null">
            <foreach collection="codeList" item="item" open=" AND code IN (" separator=", " close=")">
                #{item}
            </foreach>
        </if>
    </select>
</mapper>