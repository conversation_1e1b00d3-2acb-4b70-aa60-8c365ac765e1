package com.digiwin.escloud.aiobasic.assetrisk.dao;

import com.digiwin.escloud.aiobasic.assetrisk.model.Asset;
import com.digiwin.escloud.aiobasic.assetrisk.model.dto.ProcessDTO;
import com.digiwin.escloud.aiobasic.assetrisk.model.param.AssetParam;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface AssetMapper {

    void batchInsert(List<Asset> list);
    List<ProcessDTO> findAssetValueProcess(@Param("set")Set<String> set,@Param("assetStatus") Integer assetStatus);
    List<ProcessDTO> findRiskAnalyseProcess(@Param("set")Set<String> set);
    List<ProcessDTO> findImprovePlanProcess(@Param("set")Set<String> set);
    List<Asset> findAllByCondition(AssetParam param);
    @Select("SELECT * FROM risk_asset WHERE id =#{assetId}")
    Asset findById(String assetId);
    int updateRiskAsset(AssetParam param);
    int updateRiskStatus(AssetParam param);

    /**
     * value is not null  and value != ''
     * @param list
     * @return
     */
    List<Asset> findAssetByIds(List<String> list);
    List<Asset> findByMap(Map<String, Object> map);
    List<String> selectAssetSystemIdByType(@Param("typeList") List<String> typeList);
}
