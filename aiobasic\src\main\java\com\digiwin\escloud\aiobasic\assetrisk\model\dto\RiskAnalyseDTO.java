package com.digiwin.escloud.aiobasic.assetrisk.model.dto;

import com.digiwin.escloud.aiobasic.assetrisk.model.base.AssetRiseAssessmentBase;
import com.digiwin.escloud.aiobasic.assetrisk.model.enums.AnalyseStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.*;

@EqualsAndHashCode(callSuper = true)
@Data
public class RiskAnalyseDTO extends AssetRiseAssessmentBase {
    private String assetSystemId;
    private String assessmentId;
    private String assessmentProjectId;
    private String currControl;
    private Integer assetValue;
    private Integer riskPossibilities;
    private Integer riskVulnerability;
    private Integer riskValue;
    private String riskLevel;
    private Integer doPriority;
    private Integer riskAccept;
    private Integer remainRiskPossibilities;
    private Integer remainRiskVulnerability;
    private Integer remainRiskValue;
    private String remainRiskLevel;
    private Integer remainRiskEffect;
    private AnalyseStatus analyseStatus;
    private List<String> improvePlanIds = new ArrayList<>();

    //风险评估项目的数据
    private String riskDetail;
    private String threat;
    private String weakness;

}


