package com.digiwin.escloud.aiobasic.assetrisk.dao;

import com.digiwin.escloud.aiobasic.assetrisk.model.*;
import com.digiwin.escloud.aiobasic.assetrisk.model.dto.RiskImproveControlPlanDTO;
import com.digiwin.escloud.aiobasic.assetrisk.model.dto.RiskImprovePlanDTO;
import com.digiwin.escloud.aiobasic.assetrisk.model.param.RiskImproveControlParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface RiskImproveControlMapper {

 List<RiskImproveControlPlanType> selectAllRiskImproveControlPlanTypes(@Param("eid")Long eid);
 List<RiskImproveControlPlanType> selectImproveControlPlanTypeByType(@Param("controlPlanTypeName") String controlPlanTypeName
         ,@Param("id") String id,@Param("eid") Long eid);
 int insertRiskImproveControlPlanType(RiskImproveControlPlanType type);

 int updateRiskImproveControlPlanTypeById(RiskImproveControlPlanType type);
 int deleteRiskImproveControlPlanTypeById(String id);

 int insertRiskImproveControlPlan(RiskImproveControlPlan plan);
 int insertBatchRiskAnalyseImprovePlanMapping(List<RiskAnalyseImprovePlanMapping> plans);
 int deleteRiskAnalyseImprovePlanMapping(@Param("riskAnalyseId") String riskAnalyseId,@Param("improveControlPlanId") String improveControlPlanId);
 List<String> selectRiskAnalyseImprovePlanMappingByRiskAnalyseId(String riskAnalyseId);
 List<String> selectRiskAnalyseImprovePlanByRiskAnalyseId(String riskAnalyseId);
 List<String> selectRiskAnalyseImprovePlanMappingByControlPlanId(String improveControlPlanId);
 List<RiskImproveControlPlanDTO> selectRiskImproveControlPlanByTypeId(RiskImproveControlParam param);
 List<RiskImproveControlPlan> selectAllRiskImproveControlPlan(@Param("eid")Long eid);
 List<RiskImprovePlanDTO> selectAllRiskImproveControlPlanById(@Param("id") String id,@Param("assessmentId") String assessmentId);
 int updateRiskImproveControlPlan(RiskImproveControlPlan plan);
 int deleteRiskImproveControlPlanById(String id);

    String selectImproveControlPlanByPlan(@Param("improvePlan") String improvePlan,@Param("id") String id
            ,@Param("controlPlanTypeId")String typeId);
}
