package com.digiwin.escloud.aiobasic.assetrisk.dao;

import com.digiwin.escloud.aiobasic.assetrisk.model.RiskCurrControlPlan;
import com.digiwin.escloud.aiobasic.assetrisk.model.RiskCurrControlProject;
import com.digiwin.escloud.aiobasic.assetrisk.model.dto.RiskCurrControlDTO;
import com.digiwin.escloud.aiobasic.assetrisk.model.param.RiskCurrControlProjectParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface RiskCurrControlMapper {
    void batchInsertRiskCurrControlProject(List<RiskCurrControlProject> list);
    int insertRiskCurrControlProject(RiskCurrControlProject project);
    int insertRiskCurrControlProjectPlanAndControlMethod(RiskCurrControlPlan plan);
    List<RiskCurrControlProject> selectRiskCurrControlProjectByProjectName(String projectName);
    List<RiskCurrControlProject> selectRiskCurrControlProjectByCondition(RiskCurrControlProjectParam projectName);
    List<RiskCurrControlProject> selectRiskCurrControlProjectByProjectNameAndNull(String projectName);
    RiskCurrControlProject selectRiskCurrControlProjectById(String id);
    int selectRiskCurrControlProjectByProjectNameAndNotNull(String projectName);
    List<RiskCurrControlDTO> selectCurrControlProjectPlanAndMethod(RiskCurrControlProjectParam param);
    List<RiskCurrControlDTO> selectProjectName();
    List<RiskCurrControlDTO> selectProjectNameByCondition(RiskCurrControlProjectParam param);
    String selectControlMethodByProjectId(@Param("controlProjectId")String controlProjectId
            ,@Param("assessmentId")String assessmentId);
    RiskCurrControlPlan selectControlMethodById(@Param("id")String id,@Param("assessmentId")String assessmentId);

    int deleteRiskCurrControlProjectById(String id);
    int selectCountControlMethodById(String id);
    int deleteRiskCurrControlProjectByProjectName(String ProjectName);
    int deleteRiskCurrControlMethodByProjectIds(@Param("controlProjectIds")List<String> controlProjectIds,@Param("assessmentId") String assessmentId);
    int updateRiskCurrControlProject(RiskCurrControlProjectParam param);
    int updateControlPlanNull(String id);
    int updateRiskCurrControlMethodById(@Param("managerAndControlMethod") String managerAndControlMethod, @Param("id") String id, @Param("assessmentId") String assessmentId);
    int updateRiskCurrControlProjectByProjectName(RiskCurrControlProjectParam param);


}
