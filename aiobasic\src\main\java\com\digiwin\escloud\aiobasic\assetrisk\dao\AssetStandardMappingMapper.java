package com.digiwin.escloud.aiobasic.assetrisk.dao;

import com.digiwin.escloud.aiobasic.assetrisk.model.AssetStandardMapping;
import com.digiwin.escloud.aiobasic.assetrisk.model.AssetValueStandard;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AssetStandardMappingMapper {

    int insertRiskAssetStandardMapping(AssetStandardMapping mapping);
    int deleteRiskAssetStandardMappingById(String id);
    int updateRiskAssetStandardMapping(AssetStandardMapping mapping);

    List<AssetStandardMapping> selectRiskAssetStandardMappingByStandardIdAndSystemId(@Param("systemId") String systemId);

}
