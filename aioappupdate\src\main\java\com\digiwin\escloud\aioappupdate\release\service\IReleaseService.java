package com.digiwin.escloud.aioappupdate.release.service;

import com.digiwin.escloud.aioappupdate.release.model.BaseVersion;
import com.digiwin.escloud.aioappupdate.release.model.Release;
import com.digiwin.escloud.aioappupdate.release.model.ReleaseWhiteTenant;
import com.digiwin.escloud.common.response.BaseResponse;

import java.util.List;

public interface IReleaseService {

    BaseResponse getBaseVersionList(int pageIndex, int size, String productCode);

    BaseResponse addBaseVersion(BaseVersion baseVersion);

    BaseResponse updateBaseVersion(Long abvId, BaseVersion baseVersion);

    BaseResponse deleteBaseVersion(Long abvId);

    BaseResponse getReleaseList(int pageIndex, int size, String productCode, String baseVersion, Long abvId, String status, String releaseVersion, String releaseName, String platformVersion, String startDate, String endDate, String openWhite);

    BaseResponse getReleaseTreeList(String productCode, String baseVersion, Long abvId, String status, String releaseVersion, String releaseName, String platformVersion, String startDate, String endDate, String openWhite);

    BaseResponse<Release> getReleaseDetail(Long arId);

    BaseResponse release(String bucketName, String fileId, Release release);

    BaseResponse updateStatus(Long arId, int status);

    BaseResponse setWhiteStatus(Long arId, boolean openWhite);

    BaseResponse setWhiteTanant(Long arId, Release release);

    BaseResponse batchSetWhiteTanant(Long arId, List<ReleaseWhiteTenant> whiteTenants);

    BaseResponse deleteWhiteTanant(long arId, long arwtId);

    BaseResponse<List<ReleaseWhiteTenant>> getWhiteTanant(Long arId);

    BaseResponse deleteRelease(Long arId);

    BaseResponse getReleaseFiles(String productCode, String baseVersion, int status, int pageIndex, int size);

    BaseResponse deleteReleaseFile(Long id, Long arId, String fileId, String bucketName);

    BaseResponse getNewVersion(String productCode, String baseVersion, String platformVersion, String releaseNo, String currentVersion, String serviceCode);

}
