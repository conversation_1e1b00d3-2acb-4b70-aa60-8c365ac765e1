package com.digiwin.escloud.aiobasic.assetrisk.controller;

import com.digiwin.escloud.aiobasic.assetrisk.model.RiskCurrControlProject;
import com.digiwin.escloud.aiobasic.assetrisk.model.dto.RiskCurrControlDTO;
import com.digiwin.escloud.aiobasic.assetrisk.model.param.RiskCurrControlProjectParam;
import com.digiwin.escloud.aiobasic.assetrisk.service.RiskCurrControlService;
import com.digiwin.escloud.common.controller.ControllerBase;
import com.digiwin.escloud.common.model.ResponseBase;
import com.digiwin.escloud.common.response.BaseResponse;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

import static com.digiwin.escloud.common.model.ResponseCode.SUCCESS;

@Api(value = "/risk/curr", protocols = "HTTP", tags = {"既有控制措施相关接口"}, description = "既有控制措施相关接口")
@RestController
@RequestMapping("/risk/curr")
public class RiskCurrControlController extends ControllerBase {
    @Resource
    private RiskCurrControlService riskCurrControlService;

    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "既有控制措施项目保存")
    @PostMapping("/controlProjectSave")
    public ResponseBase controlProjectSave(@RequestBody RiskCurrControlProject project){
        return riskCurrControlService.riskCurrControlProjectSave(project);
    }
    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "批量保存控制措施和项目，一般都是一个项目多个控制措施或者一个控制措施")
    @PostMapping("/controlBatchSave")
    public ResponseBase controlBatchSave(@RequestBody RiskCurrControlProjectParam param){
       return riskCurrControlService.riskCurrControlProjectBatchSave(param);
    }


    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "根据控制措施项目名获取控制措施，分页")
    @PostMapping("/controlByProjectNameGet")
    public ResponseBase controlByProjectNameGet(@RequestBody RiskCurrControlProjectParam param){
        return  riskCurrControlService.riskCurrControlProjectGet(param);
    }

    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "获取所有控制措施项目名，无分页")
    @GetMapping("/controlProjectNameGet")
    public BaseResponse controlProjectNameGet(){
        return this.getBaseResponse((x)->{
            List<RiskCurrControlDTO> projectList = riskCurrControlService.riskCurrControlProjectGet();
            x.setData(projectList);
            return x;
        },true,false,SUCCESS);
    }

    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "获取所有控制措施项目名，分页")
    @PostMapping("/controlProjectNamePageGet")
    public BaseResponse controlProjectNamePageGet(@RequestBody RiskCurrControlProjectParam param){
        return this.getBaseResponse((x)->{
            PageInfo<RiskCurrControlDTO> projectPageList = riskCurrControlService.riskCurrControlProjectPageGet(param);
            x.setData(projectPageList);
            return x;
        },true,false,SUCCESS);
    }

    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "根据id更新控制措施的名称")
    @PutMapping("/controlProjectByIdUpdate")
    public ResponseBase controlProjectByIdUpdate(@RequestBody RiskCurrControlProjectParam param){
        return riskCurrControlService.riskCurrControlProjectUpdate(param);
    }

    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "根据控制措施的项目名称，修改所有的控制措施的项目名称（批量修改）")
    @PutMapping("/controlProjectByProjectUpdate")
    public ResponseBase controlProjectByProjectUpdate(@RequestBody RiskCurrControlProjectParam param){
        return  riskCurrControlService.riskCurrControlProjectNameUpdate(param);
    }


    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "根据控制措施id单独删除控制措施")
    @DeleteMapping("/controlPlanDelete")
    public ResponseBase controlPlanDelete(@RequestBody RiskCurrControlProjectParam param){
        return riskCurrControlService.riskCurrControlProjectDelete(param);
    }

    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "根据控制措施的项目名称（批量）删除控制措施及整个项目")
    @DeleteMapping("/controlProjectDelete")
    public ResponseBase controlProjectDelete(@RequestBody RiskCurrControlProjectParam param){
        return  riskCurrControlService.riskCurrControlProjectDeleteByProjectName(param);
    }


    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "获取控制措施项目下的所有控制措施及管理方法，分页")
    @PostMapping("/controlMethodPageGet")
    public ResponseBase controlMethodPageGet(@RequestBody RiskCurrControlProjectParam param){
        return riskCurrControlService.maintainCurrControlMethodGet(param);
    }

//    @ApiResponses({
//            @ApiResponse(code = 1, message = "内部错误"),
//            @ApiResponse(code = 0, message = "成功")
//    })
//    @ApiOperation(value = "保存控制措施项目下的控制措施及管理方法，如果管理方法没有就只保存控制措施")
//    @PostMapping("/controlMethodSave")
//    public ResponseBase controlMethodSave(@RequestBody RiskCurrControlProjectParam param){
//        return riskCurrControlService.maintainCurrControlMethodSave(param);
//    }

    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "只可以修改控制措施和管理方法，项目修改调用另一个接口")
    @PutMapping("/controlMethodUpdate")
    public ResponseBase controlMethodUpdate(@RequestBody RiskCurrControlProjectParam param){
        return riskCurrControlService.maintainCurrControlMethodUpdate(param);
    }


//    @ApiResponses({
//            @ApiResponse(code = 1, message = "内部错误"),
//            @ApiResponse(code = 0, message = "成功")
//    })
//    @ApiOperation(value = "根据控制措施的id删除控制措施及管理方法")
//    @DeleteMapping("/controlMethodDelete")
//    public ResponseBase controlMethodDelete(@RequestBody RiskCurrControlProjectParam param){
//        return riskCurrControlService.maintainCurrControlMethodDelete(param);
//    }

}
