package com.digiwin.escloud.aiobasic.assetrisk.model.dto;

import com.digiwin.escloud.aiobasic.assetrisk.model.AssetRiskAssessment;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class AssetRiskAssessmentDTO extends AssetRiskAssessment {
    private List<ProcessDTO> assetValue = new ArrayList<>();
    private List<ProcessDTO> riskAnalyse = new ArrayList<>();
    private List<ProcessDTO> improvePlan = new ArrayList<>();

    private int assetTotalCnt;
}
