package com.digiwin.escloud.aiobasic.assetrisk.controller;

import com.digiwin.escloud.aiobasic.assetrisk.model.RiskImprovePlanDetail;
import com.digiwin.escloud.aiobasic.assetrisk.model.dto.RiskImprovePlan2DTO;
import com.digiwin.escloud.aiobasic.assetrisk.model.enums.PlanStatus;
import com.digiwin.escloud.aiobasic.assetrisk.model.param.RiskImprovePlanParam;
import com.digiwin.escloud.aiobasic.assetrisk.service.RiskImprovePlanService;
import com.digiwin.escloud.aiobasic.assetrisk.service.ScheduleTask;
import com.digiwin.escloud.common.controller.ControllerBase;
import com.digiwin.escloud.common.model.ResponseBase;
import com.digiwin.escloud.common.response.BaseResponse;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import java.util.Objects;

import static com.digiwin.escloud.common.model.ResponseCode.RISK_EID_EMPTY;
import static com.digiwin.escloud.common.model.ResponseCode.SUCCESS;

@Api(value = "/risk/improve/plan", protocols = "HTTP", tags = {"改善计划接口相关"}, description = "改善计划接口相关")
@RestController
@RequestMapping("/risk/improve/plan")
public class RiskImprovePlanController extends ControllerBase {
    @Resource
    private RiskImprovePlanService riskImprovePlanService;
    @Resource
    private ScheduleTask scheduleTask;

    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "改善计划获取")
    @PostMapping("/planGet")
    public ResponseBase planGet(@RequestBody RiskImprovePlanParam param){
        return riskImprovePlanService.improvePlanGet(param);
    }

    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "制定改善计划")
    @PutMapping("/planUpdate")
    public ResponseBase planUpdate(@RequestBody RiskImprovePlanParam param){
        return riskImprovePlanService.improvePlanUpdate(param);
    }

    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "下载改善计划excel")
    @PostMapping("/planDown")
    public ResponseBase planDown(@RequestBody RiskImprovePlanParam param){
        if (Objects.isNull(param.getEid())) {
            return ResponseBase.error(RISK_EID_EMPTY);
        }
        return riskImprovePlanService.improvePlanDown(param, String.valueOf(param.getEid()));
    }

    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "查询改善计划执行详情")
    @PostMapping("/planDetailGet")
    public ResponseBase planDetailGet(@RequestBody RiskImprovePlanParam param){
        return riskImprovePlanService.improvePlanDetailGet(param);
    }


    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "改善计划审核")
    @PostMapping("/planReview")
    public ResponseBase planReview(@RequestBody RiskImprovePlanParam param){
        if (Objects.isNull(param.getEid())) {
            return ResponseBase.error(RISK_EID_EMPTY);
        }
        return riskImprovePlanService.planReview(param,param.getEid());
    }

    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "改善计划审核是否可以发起")
    @PostMapping("/isPlanReview")
    public ResponseBase isPlanReview(@RequestBody RiskImprovePlanParam param){
        if (Objects.isNull(param.getEid())) {
            return ResponseBase.error(RISK_EID_EMPTY);
        }
        return riskImprovePlanService.isPlanReview(param,param.getEid());
    }

    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "改善计划审核")
    @GetMapping("/planReview")
    public String planReview(@RequestParam("assessmentId") String assessmentId
            ,@RequestParam("planStatus") String planStatus,@RequestParam("tracker") String tracker,@RequestParam("area") String area){
        ResponseBase responseBase = riskImprovePlanService.planReview(assessmentId, PlanStatus.valueOf(planStatus), PlanStatus.IN_REVIEW, tracker);
        if (responseBase.getData().equals(0)) {
            if (area.equals("zh-CN")) {
                return "计划已经被审核！";
            }
            return "計劃已經被審核！";
        }
        if (area.equals("zh-CN")) {
            return "审核完成！";
        }
        return "審核完成！";
    }

    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "计划执行")
    @PutMapping("/planExecute")
    public ResponseBase planExecute(@RequestBody RiskImprovePlanParam param){
        return riskImprovePlanService.improvePlanExecute(param);
    }

    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "计划执行进度")
    @GetMapping("/planExecuteProcess")
    public ResponseBase planExecuteProcess(@RequestParam("assessmentId") String assessmentId){
        return riskImprovePlanService.improvePlanExecuteProcess(assessmentId);
    }

    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "计划完成")
    @PutMapping("/planComplete")
    public ResponseBase planComplete(@RequestBody RiskImprovePlanParam param){
        return riskImprovePlanService.improvePlanExecuteComplete(param);
    }

    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "改善计划日定时任务")
    @PostMapping("/riskDayEmail")
    public void riskDayEmail(){
        scheduleTask.riskDayEmail();
    }

    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "改善计划月定时任务")
    @PostMapping("/riskMonthEmail")
    public void riskMonthEmail(){
        scheduleTask.riskMonthEmail();
    }


}
