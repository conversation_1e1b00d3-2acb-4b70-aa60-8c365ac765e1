package com.digiwin.escloud.aiobasic.assetrisk.model.enums;

public enum VulnerabilityLanguage implements CommonEnum {
    ZH(1, "简体中文"),
    TW(2, "繁體中文"),
    ENGLISH(3, "english"),
    ;
    private final int code;
    private final String desc;

    VulnerabilityLanguage(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getDescription() {
        return desc;
    }


    public static VulnerabilityLanguage fromCode(int code) {
        for (VulnerabilityLanguage lang : VulnerabilityLanguage.values()) {
            if (lang.getCode() == code) {
                return lang;
            }
        }
        throw new IllegalArgumentException("No matching constant for [" + code + "]");
    }
}
