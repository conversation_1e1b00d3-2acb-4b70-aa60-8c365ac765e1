package com.digiwin.escloud.aioappupdate.release.model;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class BaseVersion {
    @ApiModelProperty("id主键")
    private Long id;
    @ApiModelProperty("sid")
    private Long sid;
    @ApiModelProperty("产品/应用编码")
    private String productCode;
    @ApiModelProperty("基版号")
    private String baseVersion;
    @ApiModelProperty("是否开启自动更新 0 不开启 | 1 开启")
    private boolean isAutoUpdate;
    @ApiModelProperty("自动更新授权方式 1 依产品服务合约 | 2 依云市场授权 | 3 不管控")
    private String authorizationMethod;
    @ApiModelProperty("备注")
    private String remark;

}
