package com.digiwin.escloud.aioappupdate.release.model;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ReleaseWhiteTenant {
    @ApiModelProperty("id主键")
    private Long id;
    @ApiModelProperty("sid")
    private Long sid;
    @ApiModelProperty("eid")
    private Long eid;
    @ApiModelProperty("版更记录主键id")
    private Long arId;
    @ApiModelProperty("客服代号")
    private String serviceCode;
    @ApiModelProperty("租户名称")
    private String tenantName;
    @ApiModelProperty("添加时间")
    private String addTime;
}
