package com.digiwin.escloud.aiobasic.assetrisk.controller;

import com.digiwin.escloud.aiobasic.assetrisk.model.RiskAnalyse;
import com.digiwin.escloud.aiobasic.assetrisk.model.param.RiskAnalyseParam;
import com.digiwin.escloud.aiobasic.assetrisk.service.RiskAnalyseService;
import com.digiwin.escloud.common.controller.ControllerBase;
import com.digiwin.escloud.common.model.ResponseBase;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Objects;

import static com.digiwin.escloud.common.model.ResponseCode.RISK_EID_EMPTY;

@Api(value = "/risk/analyse", protocols = "HTTP", tags = {"风险分析相关接口"}, description = "风险分析相关接口")
@RestController
@RequestMapping("/risk/analyse")
public class RiskAnalyseController  {
    @Resource
    private RiskAnalyseService riskAnalyseService;
    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "根据资通系统获取风险分析项目")
    @PostMapping("/analyseGet")
    public ResponseBase analyseGet(@RequestBody RiskAnalyseParam param){
        if (Objects.isNull(param.getEid())) {
            return ResponseBase.error(RISK_EID_EMPTY);
        }
       return riskAnalyseService.riskAnalyseGet(param,param.getEid());
    }

    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "获取评估id下的所有资通系统")
    @PostMapping("/analyseSystemGet")
    public ResponseBase analyseSystemGet(@RequestBody RiskAnalyseParam param){
        return riskAnalyseService.assetSystemGet(param);
    }


    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "批量保存风险分析内容")
    @PostMapping("/analyseSave")
    public ResponseBase analyseSave(@RequestBody RiskAnalyseParam param){
        return  riskAnalyseService.riskAnalyseSave(param);
    }

    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "风险分析excel数据导出")
    @PostMapping("/analyseExcel")
    public ResponseBase analyseExcel(@RequestBody RiskAnalyseParam param){
        if (Objects.isNull(param.getEid())) {
            return ResponseBase.error(RISK_EID_EMPTY);
        }
        return riskAnalyseService.excelDownload(param, String.valueOf(param.getEid()));
    }

    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "风险分析作废")
    @PutMapping("/analyseVoid")
    public ResponseBase analyseVoid(@RequestBody RiskAnalyseParam param){
        if (Objects.isNull(param.getEid())) {
            return ResponseBase.error(RISK_EID_EMPTY);
        }
        return  riskAnalyseService.riskAnalyseVoid(param,param.getEid());
    }

    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "风险分析启用")
    @PutMapping("/analyseNormal")
    public ResponseBase analyseNormal(@RequestBody RiskAnalyseParam param){
        return  riskAnalyseService.riskAnalyseNormal(param);
    }

    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "风险分析作废数据获取")
    @PostMapping("/analyseVoidGet")
    public ResponseBase analyseVoidGet(@RequestBody RiskAnalyseParam param){
        if (Objects.isNull(param.getEid())) {
            return ResponseBase.error(RISK_EID_EMPTY);
        }
        return  riskAnalyseService.riskAnalyseVoidGet(param,param.getEid());
    }




}
