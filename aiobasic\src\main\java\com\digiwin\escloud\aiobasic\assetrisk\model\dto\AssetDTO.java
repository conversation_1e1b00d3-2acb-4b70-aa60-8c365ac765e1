package com.digiwin.escloud.aiobasic.assetrisk.model.dto;

import com.digiwin.escloud.aiobasic.assetrisk.model.AssetSystem;
import lombok.Data;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

@Data
public class AssetDTO  {
    private String id;
    private Date createDate;
    private Date updateDate;
    private String assessmentId;
    private String assetNo;
    private String assetType;
    private String assessmentTypeId;
    private String assessmentType;
    private String assetName;
    private int assetStatus;
    private String assetSystem;
    private int assetNum;
    private String assetPos;
    private String assetCompany;
    private String assetOwner;
    private String assetOwnerCompany;
    private String assetMaintainStatus;
    private String assetSystemName;
    private String value;
    private Set<SystemMappingDTO> assetSystemNames = new HashSet<>();
    @Data
    public static class SystemMappingDTO{
        private String assetSystemId;
        private String assetSystemName;

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;

            if (o == null || getClass() != o.getClass()) return false;

            SystemMappingDTO that = (SystemMappingDTO) o;

            return new EqualsBuilder().append(assetSystemId, that.assetSystemId).append(assetSystemName, that.assetSystemName).isEquals();
        }

        @Override
        public int hashCode() {
            return new HashCodeBuilder(17, 37).append(assetSystemId).append(assetSystemName).toHashCode();
        }
    }


}


