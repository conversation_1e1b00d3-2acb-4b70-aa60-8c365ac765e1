package com.digiwin.escloud.aioappupdate.update.model;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class Update {
    @ApiModelProperty("id主键")
    private Long id;
    @ApiModelProperty("sid")
    private Long sid;
    @ApiModelProperty("eid")
    private Long eid;
    @ApiModelProperty("版更记录主键id")
    private Long arId;
    @ApiModelProperty("更新请求单号")
    private String updateReqNo;
    @ApiModelProperty("设备id")
    private String deviceId;
    @ApiModelProperty("设备名称")
    private String deviceName;
    @ApiModelProperty("版更方式 AUTO | MANUAL")
    private String updateMode;
    @ApiModelProperty("版更渠道 KIT | T100_TOOL")
    private String updateChannel;
    @ApiModelProperty("版更状态")
    private String status;
    @ApiModelProperty("开始时间")
    private String startTime;
    @ApiModelProperty("结束时间")
    private String endTime;

    @ApiModelProperty("产品/应用编码")
    private String productCode;
    @ApiModelProperty("产品/应用名称")
    private String productCategory;
    @ApiModelProperty("基版号")
    private String baseVersion;
    @ApiModelProperty("版更包的版本号")
    private String releaseVersion;
    @ApiModelProperty("版更包编号")
    private String releaseNo;
    @ApiModelProperty("版更包的版本名称")
    private String releaseName;
    @ApiModelProperty("租户名称")
    private String tenantName;
    @ApiModelProperty("更新历程")
    private List<UpdateProcess> processList;

}
