package com.digiwin.escloud.aioappupdate;

import com.digiwin.escloud.common.swagger.EnableSwagger3;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableAsync;


@EnableDiscoveryClient
@SpringBootApplication
@EnableFeignClients(basePackages = {"com.digiwin.escloud.common.feign"})
@EnableSwagger3
@MapperScan({"com.digiwin.escloud.aioappupdate.*.dao"})
@EnableAsync(proxyTargetClass=true)
public class AioAppUpdateApplication {
    public static void main(String[] args) {
        System.setProperty("nacos.logging.default.config.enabled", "false");
        new SpringApplicationBuilder(AioAppUpdateApplication.class).run(args);
    }
}
