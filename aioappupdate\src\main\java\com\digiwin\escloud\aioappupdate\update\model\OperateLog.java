package com.digiwin.escloud.aioappupdate.update.model;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class OperateLog {
    @ApiModelProperty("id主键")
    private Long id;
    @ApiModelProperty("sid")
    private Long sid;
    @ApiModelProperty("eid")
    private Long eid;
    @ApiModelProperty("租户名称")
    private String tenantName;
    @ApiModelProperty("设备id")
    private String deviceId;
    @ApiModelProperty("设备名称")
    private String deviceName;
    @ApiModelProperty("产品/应用编码")
    private String productCode;
    @ApiModelProperty("产品/应用名称")
    private String productCategory;
    @ApiModelProperty("基版号")
    private String baseVersion;
    @ApiModelProperty("版更包的版本号")
    private String releaseVersion;
    @ApiModelProperty("版更包编号")
    private String releaseNo;
    @ApiModelProperty("版更包的版本名称")
    private String releaseName;
    @ApiModelProperty("操作人")
    private String operator;
    @ApiModelProperty("操作")
    private String operation;
    @ApiModelProperty("操作时间")
    private String operationTime;
    @ApiModelProperty("操作状态")
    private String operatorStatus;
    @ApiModelProperty("操作人来源 KIT、T100_Tool、企业运维云用户")
    private String operatorFrom;
    @ApiModelProperty("备注")
    private String remark;
}
