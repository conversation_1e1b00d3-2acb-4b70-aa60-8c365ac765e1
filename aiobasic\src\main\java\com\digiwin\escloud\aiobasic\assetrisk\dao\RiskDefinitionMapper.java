package com.digiwin.escloud.aiobasic.assetrisk.dao;

import com.digiwin.escloud.aiobasic.assetrisk.model.AssetRiskAssessment;
import com.digiwin.escloud.aiobasic.assetrisk.model.RiskLevel;
import com.digiwin.escloud.aiobasic.assetrisk.model.RiskPossibilities;
import com.digiwin.escloud.aiobasic.assetrisk.model.RiskVulnerability;
import com.digiwin.escloud.aiobasic.assetrisk.model.dto.AssetRiskAssessmentDTO;
import com.digiwin.escloud.aiobasic.assetrisk.model.param.RiskAssessmentParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface RiskDefinitionMapper {

 int insertRisk(RiskPossibilities riskPossibilities);
 int insertVulnerability(RiskVulnerability riskPossibilities);
 int updateRisk(RiskPossibilities riskPossibilities);
 int deletePossibilitiesById(String id);
 int updateVulnerability(RiskVulnerability riskPossibilities);
 int deleteVulnerabilityById(String id);
 int insertRiskLevel(RiskLevel riskPossibilities);
 int updateRiskLevel(RiskLevel riskPossibilities);
 List<RiskPossibilities> selectRiskByAssessmentId(@Param("assessmentId") String assessmentId
         ,@Param("level")Integer level);
 List<RiskVulnerability> selectVulnerabilityByAssessmentId(@Param("assessmentId") String assessmentId,
                                                           @Param("level")Integer level);
 List<RiskLevel> selectRiskLevelByAssessmentId(@Param("assessmentId") String assessmentId);

 void insertBatchRiskPossibilities(List<RiskPossibilities> riskPossibilities);

 void insertBatchRiskVulnerability(List<RiskVulnerability> riskVulnerabilities);

 void insertBatchRiskLevel(List<RiskLevel> riskLevels);
}
