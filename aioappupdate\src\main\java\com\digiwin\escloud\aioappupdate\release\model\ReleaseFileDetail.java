package com.digiwin.escloud.aioappupdate.release.model;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ReleaseFileDetail extends ReleaseFile {
    @ApiModelProperty("产品/应用编码")
    private String productCode;
    @ApiModelProperty("产品/应用名称")
    private String productCategory;
    @ApiModelProperty("版更包的版本号")
    private String releaseVersion;
    @ApiModelProperty("版更包编号")
    private String releaseNo;
    @ApiModelProperty("版更包的版本名称")
    private String releaseName;
    @ApiModelProperty("版更记录主键id")
    private Long arId;
}
