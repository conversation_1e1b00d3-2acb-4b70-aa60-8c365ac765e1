package com.digiwin.escloud.aiobasic.assetrisk.controller;

import com.digiwin.escloud.aiobasic.assetrisk.model.RiskCurrControlProject;
import com.digiwin.escloud.aiobasic.assetrisk.model.RiskImproveControlPlan;
import com.digiwin.escloud.aiobasic.assetrisk.model.RiskImproveControlPlanType;
import com.digiwin.escloud.aiobasic.assetrisk.model.dto.RiskCurrControlDTO;
import com.digiwin.escloud.aiobasic.assetrisk.model.dto.RiskImproveControlPlanDTO;
import com.digiwin.escloud.aiobasic.assetrisk.model.param.RiskBaseParam;
import com.digiwin.escloud.aiobasic.assetrisk.model.param.RiskCurrControlProjectParam;
import com.digiwin.escloud.aiobasic.assetrisk.model.param.RiskImproveControlParam;
import com.digiwin.escloud.aiobasic.assetrisk.service.RiskCurrControlService;
import com.digiwin.escloud.aiobasic.assetrisk.service.RiskImproveControl;
import com.digiwin.escloud.common.controller.ControllerBase;
import com.digiwin.escloud.common.model.ResponseBase;
import com.digiwin.escloud.common.response.BaseResponse;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

import static com.digiwin.escloud.common.model.ResponseCode.RISK_EID_EMPTY;
import static com.digiwin.escloud.common.model.ResponseCode.SUCCESS;

@Api(value = "/risk/improve/control", protocols = "HTTP", tags = {"改善控制措施相关接口"}, description = "改善控制措施相关接口")
@RestController
@RequestMapping("/risk/improve/control")
public class RiskImproveControlController extends ControllerBase {
    @Resource
    private RiskImproveControl riskImproveControl;

    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "改善控制措施项目保存")
    @PostMapping("/typeSave")
    public ResponseBase typeSave(@RequestBody RiskImproveControlPlanType type){
        if (Objects.isNull(type.getEid())) {
            return ResponseBase.error(RISK_EID_EMPTY);
        }
        return riskImproveControl.riskImproveControlTypeSave(type,type.getEid());
    }

    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "改善控制措施项目获取")
    @GetMapping("/typeGet")
    public ResponseBase typeGet(@RequestParam("eid") Long eid){
        if (Objects.isNull(eid)) {
            return ResponseBase.error(RISK_EID_EMPTY);
        }
        List<RiskImproveControlPlanType> riskImproveControlPlanTypes = riskImproveControl.riskImproveControlTypeGet(eid);

        return ResponseBase.ok(riskImproveControlPlanTypes);
    }

    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "改善控制措施项目更新")
    @PutMapping("/typeUpdate")
    public ResponseBase typeUpdate(@RequestBody RiskImproveControlPlanType type){
        if (Objects.isNull(type.getEid())) {
            return ResponseBase.error(RISK_EID_EMPTY);
        }
        return  riskImproveControl.riskImproveControlTypeUpdate(type,type.getEid());
    }


    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "改善控制措施项目删除")
    @DeleteMapping("/typeDelete")
    public ResponseBase typeDelete(@RequestBody RiskImproveControlParam param){
        return  riskImproveControl.riskImproveControlTypeDelete(param.getId());
    }

    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "改善控制措施获取")
    @PostMapping("/controlGet")
    public ResponseBase controlGet(@RequestBody RiskImproveControlParam param){
        return riskImproveControl.riskImproveControlGet(param);
    }

    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "获取全部改善控制措施")
    @GetMapping("/controlAllGet")
    public BaseResponse controlAllGet(@RequestParam("eid")Long eid){
        return this.getBaseResponse((x)->{
            List<RiskImproveControlPlan> riskImproveControlPlans = riskImproveControl.riskImproveControlAllGet(eid);
            x.setData(riskImproveControlPlans);
            return x;
        },true,false,SUCCESS);
    }

    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "改善控制措施保存")
    @PostMapping("/controlSave")
    public ResponseBase controlSave(@RequestBody RiskImproveControlParam param){
        return riskImproveControl.riskImproveControlSave(param);
    }

    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "改善控制措施更新")
    @PutMapping("/controlUpdate")
    public ResponseBase controlUpdate(@RequestBody RiskImproveControlPlan type){
        return  riskImproveControl.riskImproveControlUpdate(type);
    }

    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "改善控制措施删除")
    @DeleteMapping("/controlDelete")
    public ResponseBase controlDelete(@RequestBody RiskImproveControlParam param){
        return riskImproveControl.riskImproveControlPlanDelete(param.getId());
    }

}
