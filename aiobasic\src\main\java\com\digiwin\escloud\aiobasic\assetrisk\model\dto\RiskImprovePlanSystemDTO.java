package com.digiwin.escloud.aiobasic.assetrisk.model.dto;

import com.digiwin.escloud.aiobasic.assetrisk.model.base.AssetRiseAssessmentBase;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Data
public class RiskImprovePlanSystemDTO  {
    private String controlPlanId;
    private String systemId;
    private String assetSystemName;

    public RiskImprovePlanSystemDTO() {
    }

    public RiskImprovePlanSystemDTO(String systemId, String assetSystemName) {
        this.systemId = systemId;
        this.assetSystemName = assetSystemName;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (o == null || getClass() != o.getClass()) return false;

        RiskImprovePlanSystemDTO that = (RiskImprovePlanSystemDTO) o;

        return new EqualsBuilder().append(systemId, that.systemId).isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37).append(systemId).toHashCode();
    }
}
