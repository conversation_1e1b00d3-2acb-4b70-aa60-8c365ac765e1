package com.digiwin.escloud.aiobasic.assetrisk.controller;

import com.digiwin.escloud.aiobasic.assetrisk.model.RiskAssessmentProject;
import com.digiwin.escloud.aiobasic.assetrisk.model.RiskAssessmentType;
import com.digiwin.escloud.aiobasic.assetrisk.model.param.RiskAssessmentProjectParam;
import com.digiwin.escloud.aiobasic.assetrisk.model.param.RiskAssessmentTypeParam;
import com.digiwin.escloud.aiobasic.assetrisk.service.RiskProjectService;
import com.digiwin.escloud.common.controller.ControllerBase;
import com.digiwin.escloud.common.model.ResponseBase;
import com.digiwin.escloud.common.response.BaseResponse;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

import static com.digiwin.escloud.common.model.ResponseCode.RISK_EID_EMPTY;
import static com.digiwin.escloud.common.model.ResponseCode.SUCCESS;

@Api(value = "/risk/project", protocols = "HTTP", tags = {"风险项目识别相关接口"}, description = "风险项目识别相关接口")
@RestController
@RequestMapping("/risk/project")
public class RiskProjectController extends ControllerBase {
    @Resource
    private RiskProjectService riskAssessmentSave;
    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "保存风险评估类型")
    @PostMapping("/typeSave")
    public ResponseBase typeSave(@RequestBody RiskAssessmentTypeParam type){
        if (Objects.isNull(type.getEid())) {
            return ResponseBase.error(RISK_EID_EMPTY);
        }
       return riskAssessmentSave.riskTypeSave(type,type.getEid());
    }

    @ApiResponses({
            @ApiResponse(code = 1, message = "更新失败"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "更新风险评估类型")
    @PutMapping("/typeUpdate")
    public ResponseBase typeUpdate(@RequestBody RiskAssessmentTypeParam type){
        return  riskAssessmentSave.riskTypeUpdate(type);
    }

    @ApiResponses({
            @ApiResponse(code = 1, message = "删除失败"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "删除风险评估类型")
    @DeleteMapping("/typeDelete")
    public ResponseBase typeDelete(@RequestBody RiskAssessmentTypeParam type){
        if (Objects.isNull(type.getEid())) {
            return ResponseBase.error(RISK_EID_EMPTY);
        }
        return  riskAssessmentSave.riskTypeDelete(type.getId(),type.getEid());
    }

    @ApiResponses({
            @ApiResponse(code = 1, message = "删除失败"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "获取所有风险评估类型，没有分页，这个也是资产类型")
    @GetMapping("/typeGet")
    public BaseResponse typeGet(@RequestParam(value = "typeName",required = false) String typeName){
        return this.getBaseResponse((x)->{
            List<RiskAssessmentType> riskAssessmentTypes = riskAssessmentSave.riskTypeSelect(typeName);
            x.setData(riskAssessmentTypes);
            return x;
        },true,false,SUCCESS);
    }


    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "保存风险评估类型，风险评估类别必传 typeId")
    @PostMapping("/projectSave")
    public ResponseBase projectSave(@RequestBody RiskAssessmentProject project){
        if (Objects.isNull(project.getEid())) {
            return ResponseBase.error(RISK_EID_EMPTY);
        }
        return riskAssessmentSave.riskProjectSave(project,project.getEid());
    }

    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "更新风险评估类型，只可以更新威胁来源、威胁、脆弱性、风险描述，其他更新不了")
    @PutMapping("/projectUpdate")
    public ResponseBase projectUpdate(@RequestBody RiskAssessmentProject project){
        return  riskAssessmentSave.riskProjectUpdate(project);
    }

    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "删除风险评估类型，只可以根据id删除，id必传")
    @DeleteMapping("/projectDelete")
    public ResponseBase projectDelete(@RequestBody RiskAssessmentProject project){
        if (Objects.isNull(project.getEid())) {
            return ResponseBase.error(RISK_EID_EMPTY);
        }
        return riskAssessmentSave.riskProjectDelete(project,project.getEid());
    }

    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "分页查询风险评估项目，风险评估类别必传 typeId,不传会查询出当前页的所有的数据")
    @PostMapping("/projectGet")
    public ResponseBase projectGet(@RequestBody RiskAssessmentProjectParam param){

        if (Objects.isNull(param.getEid())) {
            return ResponseBase.error(RISK_EID_EMPTY);
        }
        PageInfo riskAssessmentProjectPageInfo = riskAssessmentSave.riskProjectSelect(param,param.getEid());
        return ResponseBase.ok(riskAssessmentProjectPageInfo);
    }


}
