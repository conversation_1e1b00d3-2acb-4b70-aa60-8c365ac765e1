package com.digiwin.escloud.aiobasic.assetrisk.model.enums;

public enum ScanBatchEnum  {
    FIRST("0", "初扫","初掃"),
    SECOND("1", "复扫","複掃"),

    ;
    private final String code;
    private final String desc;
    private final String desc_tw;

    ScanBatchEnum(String code, String desc,String desc_tw) {
        this.code = code;
        this.desc = desc;
        this.desc_tw = desc_tw;
    }



    public String getDescription() {
        return desc;
    }
    public String getDescription_tw() {
        return desc_tw;
    }

    public static ScanBatchEnum getByCode(String code) {
        for (ScanBatchEnum scanBatchEnum : ScanBatchEnum.values()) {
            if (scanBatchEnum.code.equals(code)) {
                return scanBatchEnum;
            }
        }
        return FIRST; // 如果找不到匹配的枚举值，返回默认
    }
}
