package com.digiwin.escloud.aiobasic.assetrisk.controller;

import com.digiwin.escloud.aiobasic.assetrisk.model.AssetValueStandard;
import com.digiwin.escloud.aiobasic.assetrisk.model.RiskAssessmentProject;
import com.digiwin.escloud.aiobasic.assetrisk.model.RiskAssessmentType;
import com.digiwin.escloud.aiobasic.assetrisk.model.param.RiskAssessmentProjectParam;
import com.digiwin.escloud.aiobasic.assetrisk.model.param.RiskAssessmentTypeParam;
import com.digiwin.escloud.aiobasic.assetrisk.service.AssetValueStandardService;
import com.digiwin.escloud.aiobasic.assetrisk.service.RiskProjectService;
import com.digiwin.escloud.common.controller.ControllerBase;
import com.digiwin.escloud.common.model.ResponseBase;
import com.digiwin.escloud.common.response.BaseResponse;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

import static com.digiwin.escloud.common.model.ResponseCode.SUCCESS;

@Api(value = "/risk/standard", protocols = "HTTP", tags = {"资产鉴值评估准则相关接口"}, description = "资产鉴值评估准则相关接口")
@RestController
@RequestMapping("/risk/standard")
public class RiskAssetStandardController {
    @Resource
    private AssetValueStandardService assetValueStandardService;
    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "资产鉴值评估是否可以修改")
    @GetMapping("/isAssetStandardChoose")
    public ResponseBase isAssetStandardChoose(@RequestParam("assessmentId")String assessmentId){
       return assetValueStandardService.isAssetStandardChoose(assessmentId);
    }

    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "资产鉴值评估准则获取")
    @GetMapping("/assetStandardGet")
    public ResponseBase assetStandardGet(@Param("assessmentId") String assessmentId, @Param("level") Integer level) {
        return assetValueStandardService.riskAssetValueStandardGet(assessmentId, level);
    }

    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "资产鉴值评估准则更新")
    @PutMapping("/assetStandardUpdate")
    public ResponseBase assetStandardUpdate(@RequestBody AssetValueStandard standard) {
        return assetValueStandardService.riskAssetValueStandardUpdate(standard.getStandards());
    }

//    @ApiResponses({
//            @ApiResponse(code = 1, message = "内部错误"),
//            @ApiResponse(code = 0, message = "成功")
//    })
//    @ApiOperation(value = "资产鉴值评估准则删除")
//    @DeleteMapping("/assetStandardDelete")
//    public ResponseBase assetStandardDelete(@RequestBody AssetValueStandard standard){
//        return  assetValueStandardService.riskAssetValueStandardDelete(standard);
//    }


}
