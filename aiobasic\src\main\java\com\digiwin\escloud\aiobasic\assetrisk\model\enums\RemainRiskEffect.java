package com.digiwin.escloud.aiobasic.assetrisk.model.enums;


public enum RemainRiskEffect implements CommonEnum {
    ACCEPT(0, "可接受"),
    ;

    private final int code;
    private final String desc;

    RemainRiskEffect(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getDescription() {
        return desc;
    }

    public static RemainRiskEffect getEnumByCode(Integer code) {
        for (RemainRiskEffect e : RemainRiskEffect.values()) {
            if (e.match(String.valueOf(code))) {
                return e;
            }
        }
        return null;
    }

}
