package com.digiwin.escloud.aioappupdate.update.model;

import com.digiwin.escloud.common.model.CodeNameBase;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("更新操作")
@Data
public class UpdateOperation extends CodeNameBase {
    @ApiModelProperty("结果类型(SUCCESS:成功；PASS:通过；FAIL:失败)")
    private String resultType;
    @ApiModelProperty(value = "语言别处理后名称，前端不关注", hidden = true)
    @JsonIgnore
    private String currentName;
}
