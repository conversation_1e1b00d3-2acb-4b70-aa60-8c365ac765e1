package com.digiwin.escloud.aioappupdate.update.service;

import com.digiwin.escloud.aioappupdate.update.model.OperateLog;
import com.digiwin.escloud.aioappupdate.update.model.Update;
import com.digiwin.escloud.aioappupdate.update.model.UpdateProcess;
import com.digiwin.escloud.common.response.BaseResponse;

import java.util.List;

public interface IUpdateService {

  BaseResponse getUpdateLogList(int pageIndex, int size, String productCode, String baseVersion, String deviceName,
                                String tenantName, String releaseName, String releaseVersion, String status,
                                String startDate, String endDate, String updateChannel);

  BaseResponse sendRequest(String productCode, String baseVersion, String platformVersion, String releaseVersion,
                           String releaseNo, String serviceCode, String collectCode, String warningCode,
                           Update update);

  BaseResponse saveUpdateLog(String productCode, String baseVersion, String platformVersion, String releaseVersion,
                             String serviceCode, String updateReqNo, String collectCode, String warningCode,
                             Update update);

  BaseResponse getStatus(String updateReqNo);

  BaseResponse<List<UpdateProcess>> getUpdateProgress(Long auId);

  BaseResponse stopUpdate(Long id, String status, String operationContent);

  BaseResponse getOperateLogList(int pageIndex, int size, String tenantName, String deviceName,String productCode, String operation, String status, String releaseVersion, String releaseName, String startDate, String endDate);

  BaseResponse saveOperateLog(String serviceCode, OperateLog operateLog);

  /**
   * 获取更新操作列表
   * @param resultType 结果类型(SUCCESS:成功；PASS:通过；FAIL:失败)
   * @return 更新操作列表
   */
  BaseResponse getUpdateOperationList(String resultType);
}