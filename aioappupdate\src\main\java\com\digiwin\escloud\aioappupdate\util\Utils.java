package com.digiwin.escloud.aioappupdate.util;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.xml.bind.DatatypeConverter;
import java.io.*;
import java.security.MessageDigest;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by lizihua on 2017/9/26.
 */
public class Utils {

    private static Log log = LogFactory.getLog(com.digiwin.escloud.aioappupdate.util.Utils.class);

    private final static String SID = "sid";

    private final static String EID = "eid";

    public static String writeToFile(MultipartFile file, String pathPrefix, String namePrefix) {
        String orginFileName = file.getOriginalFilename();
        if (orginFileName != null && !orginFileName.equals("") && orginFileName.contains("\\")) {
            orginFileName = orginFileName.substring(orginFileName.lastIndexOf("\\") + 1, orginFileName.length());
        }
        String realFileName = pathPrefix + namePrefix + orginFileName;
        try {
            //解决文件名乱码
            realFileName = new String(realFileName.getBytes("utf-8"));
            log.info("----------" + realFileName);
            FileOutputStream fos = new FileOutputStream(new File(realFileName));
            fos.write(file.getBytes());
            fos.close();
        } catch (Exception e) {
            log.error(e.getStackTrace());
            e.printStackTrace();
            return null;
        }
        return realFileName;
    }

    public static String md5Generate(String str) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            md.update(str.getBytes());
            byte[] digest = md.digest();
            String myHash = DatatypeConverter
                    .printHexBinary(digest);
            return myHash;
        } catch (Exception e) {
            log.error(e.getStackTrace());
            e.printStackTrace();
            return null;
        }
    }

    public static String md5Generate(byte[] bytes) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            md.update(bytes);
            byte[] digest = md.digest();
            String myHash = DatatypeConverter
                    .printHexBinary(digest);
            return myHash;
        } catch (Exception e) {
            log.error(e.getStackTrace());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 将文件转换成byte数组
     *
     * @param tradeFile
     * @return
     */
    public static byte[] file2byte(File tradeFile) {
        byte[] buffer = null;
        try {
            FileInputStream fis = new FileInputStream(tradeFile);
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            byte[] b = new byte[1024];
            int n;
            while ((n = fis.read(b)) != -1) {
                bos.write(b, 0, n);
            }
            fis.close();
            bos.close();
            buffer = bos.toByteArray();
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return buffer;
    }



//    public static String getHeaderSid() {
//        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
//        return request.getHeader(SID);
//    }
//
//    public static String getHeaderEid() {
//        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
//        return request.getHeader(EID);
//    }

    public static long getHeaderSid() {
        RequestAttributes attributes = RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            return 0;
        }
        HttpServletRequest request = ((ServletRequestAttributes) attributes).getRequest();
        String sid = request.getHeader(SID);
        if (StringUtils.isEmpty(sid)) {
            return 0;
        }
        return Long.parseLong(sid);
    }

    public static long getHeaderEid() {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        String eid = request.getHeader(EID);
        if (StringUtils.isEmpty(eid)) {
            return 0;
        }
        return Long.parseLong(eid);
    }

    /**
     * 过滤HTML标签输出文本
     *
     * @param inputString 原字符串
     * @return 过滤后字符串
     */
    public static String html2Text(String inputString) {
        if (StringUtils.isEmpty(inputString)) {
            return "";
        }

        // 含html标签的字符串
        String htmlStr = inputString.trim();
        String textStr = "";
        Pattern p_script;
        Matcher m_script;
        Pattern p_style;
        Matcher m_style;
        Pattern p_html;
        Matcher m_html;
        Pattern p_space;
        Matcher m_space;
        Pattern p_escape;
        Matcher m_escape;

        try {
            //定義HTML註解的正则表达式
            String regEx_remark =  "<!--.[^-]*(?=-->)-->";

            // 定义script的正则表达式{或<script[^>]*?>[\\s\\S]*?<\\/script>
            String regEx_script = "<[\\s]*?script[^>]*?>[\\s\\S]*?<[\\s]*?\\/[\\s]*?script[\\s]*?>";

            // 定义style的正则表达式{或<style[^>]*?>[\\s\\S]*?<\\/style>
            String regEx_style = "<[\\s]*?style[^>]*?>[\\s\\S]*?<[\\s]*?\\/[\\s]*?style[\\s]*?>";

            // 定义HTML标签的正则表达式
            String regEx_html = "<[^>]+>";

            // 定义空格回车换行符
            String regEx_space = "\\s*|\t|\r|\n";

            // 定义转义字符
            String regEx_escape = "&.{2,6}?;";

            // 过滤HTML註解标签
            p_script = Pattern.compile(regEx_remark, Pattern.CASE_INSENSITIVE);
            m_script = p_script.matcher(htmlStr);
            htmlStr = m_script.replaceAll("");

            // 过滤script标签
            p_script = Pattern.compile(regEx_script, Pattern.CASE_INSENSITIVE);
            m_script = p_script.matcher(htmlStr);
            htmlStr = m_script.replaceAll("");

            // 过滤style标签
            p_style = Pattern.compile(regEx_style, Pattern.CASE_INSENSITIVE);
            m_style = p_style.matcher(htmlStr);
            htmlStr = m_style.replaceAll("");

            // 过滤html标签
            p_html = Pattern.compile(regEx_html, Pattern.CASE_INSENSITIVE);
            m_html = p_html.matcher(htmlStr);
            htmlStr = m_html.replaceAll("");

            // 过滤空格回车标签
            p_space = Pattern.compile(regEx_space, Pattern.CASE_INSENSITIVE);
            m_space = p_space.matcher(htmlStr);
            htmlStr = m_space.replaceAll("");

            // 过滤转义字符
            p_escape = Pattern.compile(regEx_escape, Pattern.CASE_INSENSITIVE);
            m_escape = p_escape.matcher(htmlStr);
            htmlStr = m_escape.replaceAll("");

            textStr = htmlStr;

        } catch (Exception e) {
            log.error( e.getMessage());
        }

        // 返回文本字符串
        return textStr;
    }
}
