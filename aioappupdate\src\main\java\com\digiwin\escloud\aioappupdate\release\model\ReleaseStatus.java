package com.digiwin.escloud.aioappupdate.release.model;


public enum ReleaseStatus {
    DRAFT(0), //草稿
    RELEASE(1), //已发布
    CANCEL_RELEASE(2); //取消发布

    private int code;
    ReleaseStatus(int code) {
        this.code = code;
    }

    public boolean isSame(int code) {
        return this.code == code;
    }

    public boolean isSame(String name) {
        return this.name().equals(name);
    }

    public int getCode() {
        return code;
    }
}
