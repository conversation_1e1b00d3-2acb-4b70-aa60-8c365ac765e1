package com.digiwin.escloud.aiobasic.assetrisk.controller;

import com.digiwin.escloud.aiobasic.assetrisk.model.AssetValueStandard;
import com.digiwin.escloud.aiobasic.assetrisk.model.param.RiskDefinitionParam;
import com.digiwin.escloud.aiobasic.assetrisk.service.AssetValueStandardService;
import com.digiwin.escloud.aiobasic.assetrisk.service.RiskDefinitionService;
import com.digiwin.escloud.common.controller.ControllerBase;
import com.digiwin.escloud.common.model.ResponseBase;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;

@Api(value = "/risk/definition", protocols = "HTTP", tags = {"风险分析定义相关接口"}, description = "风险分析定义相关接口")
@RestController
@RequestMapping("/risk/definition")
public class RiskDefinitionController extends ControllerBase {
    @Resource
    private RiskDefinitionService riskDefinitionService;

    @Resource
    private AssetValueStandardService assetValueStandardService;
    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "风险评估定义获取")
    @GetMapping("/definitionGet")
    public ResponseBase definitionGet(String assessmentId, Integer level){
       return riskDefinitionService.riskDefinitionGet(assessmentId,level);
    }

    @ApiResponses({
            @ApiResponse(code = 1, message = "内部错误"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "资产鉴值准则最高值获取")
    @GetMapping("/standardMaxGet")
    public ResponseBase standardMaxGet(String assessmentId){
        ResponseBase<List<AssetValueStandard>> listResponseBase = assetValueStandardService.riskAssetValueStandardGet(assessmentId, null);
        Integer maxScore = listResponseBase.getData().stream()
                        .max(Comparator.comparingInt(AssetValueStandard::getScore))
                .map(AssetValueStandard::getScore).orElse(0);
        return ResponseBase.ok(maxScore);
    }

    @ApiResponses({
            @ApiResponse(code = 1, message = "更新失败"),
            @ApiResponse(code = 0, message = "成功")
    })
    @ApiOperation(value = "风险评估定义保存，更新，如果有定义id就会更新，没有定义id就会新增保存")
    @PostMapping("/definitionSave")
    public ResponseBase definitionSave(@RequestBody RiskDefinitionParam param){
        return  riskDefinitionService.riskDefinitionSave(param);
    }




}
