package com.digiwin.escloud.aiobasic.assetrisk.model.enums;

public enum VulnerabilityReportStatus implements CommonEnum {
    GENERATING(1, "生成中","生成中"),
    ALREADY_GENERATED(2, "已生成","已生成"),
    ALREADY_SEND(3, "已发送","已發送"),
    DELETE(4, "删除"),
    ;
    private final int code;
    private final String desc;
    private final String desc_tw;

    VulnerabilityReportStatus(int code, String desc) {
        this.code = code;
        this.desc = desc;
        desc_tw = null;
    }
    VulnerabilityReportStatus(int code, String desc,String desc_tw) {
        this.code = code;
        this.desc = desc;
        this.desc_tw = desc_tw;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getDescription() {
        return desc;
    }

    public String getDescription_TW() {
        return desc_tw;
    }
}
