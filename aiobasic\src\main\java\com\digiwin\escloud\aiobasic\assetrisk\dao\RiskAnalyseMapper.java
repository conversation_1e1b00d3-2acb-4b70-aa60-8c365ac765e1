package com.digiwin.escloud.aiobasic.assetrisk.dao;

import com.digiwin.escloud.aiobasic.assetrisk.model.RiskAnalyse;
import com.digiwin.escloud.aiobasic.assetrisk.model.RiskCurrControlPlan;
import com.digiwin.escloud.aiobasic.assetrisk.model.RiskCurrControlProject;
import com.digiwin.escloud.aiobasic.assetrisk.model.dto.RiskAnalyseCountDTO;
import com.digiwin.escloud.aiobasic.assetrisk.model.dto.RiskAnalyseDTO;
import com.digiwin.escloud.aiobasic.assetrisk.model.dto.RiskCurrControlDTO;
import com.digiwin.escloud.aiobasic.assetrisk.model.enums.AnalyseStatus;
import com.digiwin.escloud.aiobasic.assetrisk.model.param.RiskCurrControlProjectParam;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface RiskAnalyseMapper {


    int insertRiskAnalyse(RiskAnalyse riskAnalyse);
    int updateRiskAnalyse(RiskAnalyse riskAnalyse);
    List<RiskAnalyse> selectByAssetSystemId(String systemId);
    List<RiskAnalyseDTO> selectByAssetSystemDTOId(@Param("assetSystemId") String assetSystemId,@Param("analyseStatus") AnalyseStatus analyseStatus);
    List<RiskAnalyseDTO> selectByAssetSystemDTOIds(@Param("systemIds") Set<String> systemIds);
    RiskAnalyse selectByProjectId(String projectId);
    int selectAnalyseCompleteCntBySystemId(String assetSystemId);
    int selectAllCntBySystemId(String assetSystemId);
    @MapKey("analyse")
    Map<Integer,RiskAnalyseCountDTO> selectCntBySystemId( @Param("assetSystemId") String assetSystemId);
    @MapKey("analyse")
    Map<Integer,RiskAnalyseCountDTO> selectRemainCntBySystemId( @Param("assetSystemId") String assetSystemId);

    int updateAssetValueByProjectId(@Param("assessmentProjectId") String assessmentProjectId,@Param("assetValue") Integer assetValue );

    int deleteRiskAnalyseByProjectId(String assessmentProjectId);
    int deleteRiskAnalyseById(String id);
    RiskAnalyseDTO selectRiskAnalyseById(String id);
}
