package com.digiwin.escloud.aiobasic.assetrisk.model.enums;


public enum VulnerabilityLevelEnum implements CommonEnum {
    CRITICAL(1, "严重风险"),
    HIGH(2, "高风险"),
    MEDIUM(3, "中风险"),
    LOW(4, "低风险"),
    INFO(5, "资讯"),
    ;
    private final int code;
    private final String desc;

    VulnerabilityLevelEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getDescription() {
        return desc;
    }
}
