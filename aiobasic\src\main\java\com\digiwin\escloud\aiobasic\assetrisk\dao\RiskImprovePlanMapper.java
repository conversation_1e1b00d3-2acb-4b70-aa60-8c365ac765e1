package com.digiwin.escloud.aiobasic.assetrisk.dao;

import com.digiwin.escloud.aiobasic.assetrisk.model.RiskImprovePlan;
import com.digiwin.escloud.aiobasic.assetrisk.model.RiskImprovePlanDetail;
import com.digiwin.escloud.aiobasic.assetrisk.model.dto.ProcessDTO;
import com.digiwin.escloud.aiobasic.assetrisk.model.dto.RiskImprovePlan2DTO;
import com.digiwin.escloud.aiobasic.assetrisk.model.dto.RiskImprovePlanSystemDTO;
import com.digiwin.escloud.aiobasic.assetrisk.model.param.RiskImprovePlanParam;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

public interface RiskImprovePlanMapper {
    int insertRiskImprovePlan(RiskImprovePlan plan);
    List<RiskImprovePlan> selectRiskImprovePlanByImproveId(@Param("ids") List<String> ids
            ,@Param("assessmentId") String assessmentId);
    List<RiskImprovePlanSystemDTO> selectEffectSystemByControlPlanIds(@Param("controlPlanIds")List<String> controlPlanIds
            ,@Param("assessmentId") String assessmentId);
    List<RiskImprovePlan2DTO> selectAllByCondition(RiskImprovePlanParam param);
    @Select("select * from risk_improve_plan where id = #{id}")
    RiskImprovePlan selectById(@Param("id") String id);
    List<RiskImprovePlan2DTO> selectAllByScheduleCondition(RiskImprovePlanParam param);
    int updateRiskImprovePlan(RiskImprovePlan plan);
    int updateExecuteRiskImprovePlan(RiskImprovePlan plan);
    int updateRiskImprovePlanStatusByAssessmentId(RiskImprovePlan plan);
    int insertImproveDetail(RiskImprovePlanDetail detail);
    int deletePlanById(String id);
    List<RiskImprovePlanDetail> selectDetailByPlanId(String planId);
    List<ProcessDTO> selectExecuteProcessByAssessmentId(String assessmentId);

   List<String> selectAllEidByAssessmentId(@Param("assessmentIds") List<String> assessmentIds);

}
